import gradio as gr
import pandas as pd
from langchain_openai import AzureChatOpenAI
from browser_use import Agent
from browser_use.browser.browser import <PERSON><PERSON><PERSON>, BrowserConfig
from browser_use.agent.service import AuLoadExecutionDataForRerun
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import BaseMessage, AIMessage
from langchain_core.outputs import ChatResult, ChatGeneration
from pydantic import Field
import asyncio
from dotenv import load_dotenv
import datetime
import os
import subprocess
from playwright.sync_api import sync_playwright
from PIL import Image
import configparser
import sys
import time
import json
import logging
from pathlib import Path

if sys.stdout is None:
    sys.stdout = open(os.devnull, 'w')
if sys.stderr is None:
    sys.stderr = open(os.devnull, 'w')

config_path = os.path.join('config', 'config.ini')

def read_config(config_path):
    try:
        config_parser.read(config_path)
        return config_parser
    except FileNotFoundError:
        print(f"Config file not found at: {config_path}")
        return None

max_num_of_steps = 15
preview_image_path = "./assets/images/white.jpg" 
enable_context = False
load_in_browser = True

if os.path.exists(config_path):
    config_parser = configparser.ConfigParser()

    config = read_config(config_path)
    if config:
        print("Config File Loaded.")
        max_num_of_steps = config.getint('settings', 'max_num_of_steps', fallback=max_num_of_steps)
        preview_image_path = config.get('settings', "preview_image_path", fallback=preview_image_path)
        enable_context = config.getboolean('settings', "enable_context", fallback=enable_context)
        load_in_browser = config.getboolean('settings', "load_in_browser", fallback=load_in_browser)

print(f"max_num_of_steps: {max_num_of_steps}")

cache_dir = os.path.join(os.getenv("USERPROFILE"), ".cache", "ms-playwright")

if not os.path.exists(cache_dir):
    try:
        subprocess.run(["playwright", "install"], check=True)
        print("Playwright successfully installed.")
    except Exception as e:
        print(f"Error installing Playwright: {e}")

output_folder = "outputs"

if not os.path.exists(output_folder):
    os.makedirs(output_folder)
    print(f"Folder '{output_folder}' created.")
else:
    print(f"Folder '{output_folder}' already exists.")

load_dotenv()

# ==================== HELPER CLASSES ====================

class RerunFailedResult:
    """Unified class for handling failed rerun results to avoid code duplication"""
    def __init__(self, partial_history=None, error_msg="Unknown error", failed_step=None):
        self.history = partial_history if partial_history is not None else []
        self._is_failed = True
        self._failure_reason = error_msg
        self._failed_step = failed_step

    def is_done(self):
        return True

    @property
    def success(self):
        return False

# ==================== GLOBAL VARIABLES ====================

empty_df = pd.DataFrame(columns=["Action", "Goal", "XPath", "Value"])

def open_pdf():
    pdf_file_path = os.path.abspath(os.path.join(output_folder, "test_case_report.pdf"))
    if os.path.exists(pdf_file_path):
        pdf_file_url = f"file:///{pdf_file_path.replace(os.sep, '/')}"  
        print(f"Opening: {pdf_file_url}")
        edge_path = r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe"
        chrome_path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"
        
        try:
            subprocess.run([edge_path, pdf_file_path])
        except FileNotFoundError:
            print("Microsoft Edge not found, trying Google Chrome.")
            try:
                subprocess.run([chrome_path, pdf_file_path])
            except FileNotFoundError:
                print("Google Chrome not found, please install one of the browsers.")
    else:
        print("PDF file does not exist!")


def modify_gif():
    input_gif =  os.path.join(output_folder, "agent_history.gif")
    output_gif =  os.path.join(output_folder, "output.gif")
    
    if not os.path.exists(input_gif):
        return

    gif = Image.open(input_gif)

    frames = []

    try:
        gif.seek(1) 
        while True:
            frame = gif.copy()
            frames.append(frame)
            gif.seek(gif.tell() + 1)
    except EOFError:
        pass

    if frames:
        frames[0].save(output_gif, save_all=True, append_images=frames[1:], duration=gif.info['duration'], loop=gif.info['loop'])
        print(f"New GIF created without the first frame: {output_gif}")
        try:
            gif.close()
            os.remove(input_gif)
        except Exception as e:
            pass
    else:
        print("No frames found after the first one.")

def modify_rerun_gif():
    """
    Create rerun GIF by processing the rerun_history.gif file
    """
    input_gif = os.path.join(output_folder, "rerun_history.gif")
    output_gif = os.path.join(output_folder, "rerun_output.gif")

    print(f"🔍 Looking for rerun GIF at: {input_gif}")
    print(f"🔍 Output GIF will be: {output_gif}")

    if not os.path.exists(input_gif):
        print(f"⚠️ Rerun GIF not found: {input_gif}")
        # Check if there are any GIF files in the output folder
        try:
            gif_files = [f for f in os.listdir(output_folder) if f.endswith('.gif')]
            print(f"🔍 Available GIF files in {output_folder}: {gif_files}")
        except Exception as e:
            print(f"🔍 Error listing files: {e}")
        return

    try:
        gif = Image.open(input_gif)
        frames = []

        # Process frames (remove first frame like normal execution)
        try:
            gif.seek(1)
            while True:
                frame = gif.copy()
                frames.append(frame)
                gif.seek(gif.tell() + 1)
        except EOFError:
            pass

        if frames:
            frames[0].save(output_gif, save_all=True, append_images=frames[1:],
                          duration=gif.info.get('duration', 500),
                          loop=gif.info.get('loop', 0))
            print(f"🎬 Rerun GIF created: {output_gif}")
            try:
                gif.close()
                os.remove(input_gif)
            except Exception as e:
                pass
        else:
            print("⚠️ No frames found in rerun GIF after the first one.")

    except Exception as e:
        print(f"❌ Error processing rerun GIF: {e}")

def is_chrome_running():
    tasklist_output = subprocess.run('tasklist /FI "IMAGENAME eq chrome.exe"', shell=True, capture_output=True, text=True)
    return "chrome.exe" in tasklist_output.stdout

def close_chrome():
    try:
        with sync_playwright() as p:
            browser = p.chromium.connect_over_cdp("http://localhost:9222")

            context = browser.contexts[0]
            pages = context.pages

            for page in pages:
                page.close()

            browser.close()
    except Exception as e:
        print(f"Error:{e}")

    if is_chrome_running():
        print("Chrome is running. Killing...")
        user_data_dir = "C:/temp/chrome-profile"
        find_command = f'wmic process where "CommandLine like \'%%{user_data_dir}%%\' and name=\'chrome.exe\'" get ProcessId'
        result = subprocess.check_output(find_command, shell=True).decode()
        pids = [line.strip() for line in result.splitlines() if line.strip().isdigit()]
        for pid in pids:
            print(f"Killing Chrome PID {pid}...")
            subprocess.call(f"taskkill /F /T /PID {pid}", shell=True)
        print("Chrome has been terminated.")
    else:
        print("Chrome is not running.")

def launch_chrome():
    chrome_command = r'"C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir="C:/temp/chrome-profile"'
    chrome_process = subprocess.Popen(chrome_command, shell=True)
    print(chrome_process.pid)


# ==================== SAVE FUNCTION FOR RERUN FUNCTIONALITY ====================

def AuSaveSuccessfulActionsForRerun(agent_history, task: str, execution_time: int) -> bool:
    """
    ==================== AuSaveSuccessfulActionsForRerun - START ====================
    Save only successful actions from completed task for rerun functionality.

    Args:
        agent_history: Complete execution history from agent.run() (AgentHistoryList)
        task: Original task description
        execution_time: Total execution time in seconds

    Returns:
        True if saved successfully, False otherwise
    ==================== AuSaveSuccessfulActionsForRerun - END ====================
    """
    try:
        # Only save if task completed successfully
        if not agent_history.is_done() or not agent_history.is_successful():
            print("⚠️ Task not completed successfully - not saving for rerun")
            return False

        print(f"✅ Task completed successfully - saving {len(agent_history.history)} steps for rerun")

        # Extract only successful actions (no errors)
        successful_actions = []

        for i, history_item in enumerate(agent_history.history):
            if not history_item.model_output:
                continue

            # Check if ALL results in this step were successful (no errors)
            # SIMPLIFIED: Only check execution errors, ignore LLM evaluation context
            step_successful = all(not result.error for result in history_item.result)


            # step_no_errors = all(not result.error for result in history_item.result)

            # # ALSO check if LLM evaluation indicates success (not just no errors)
            # llm_evaluation = history_item.model_output.current_state.evaluation_previous_goal.lower()
            # llm_indicates_success = any(indicator in llm_evaluation for indicator in ['success', 'successful', 'completed', 'achieved'])
            # llm_indicates_failure = any(indicator in llm_evaluation for indicator in ['failed', 'failure', 'error', 'unsuccessful', 'wrong'])

            # # Only save if: no execution errors AND (LLM indicates success OR no clear failure indication)
            # step_successful = step_no_errors and (llm_indicates_success or not llm_indicates_failure)

            # # Debug output for filtering decisions
            # if not step_no_errors:
            #     print(f"  ⚠️ Step {i+1}: Skipping due to execution errors")
            # elif llm_indicates_failure:
            #     print(f"  ⚠️ Step {i+1}: Skipping due to LLM failure indication: '{llm_evaluation}'")
            # elif step_successful:
            #     print(f"  ✅ Step {i+1}: Including - no errors and LLM indicates success/neutral")


            # Debug output for filtering decisions
            if step_successful:
                print(f"  ✅ Step {i+1}: Including - no execution errors")
            else:
                print(f"  ⚠️ Step {i+1}: Skipping - has execution errors")

            if step_successful:
                # Extract actions from this successful step
                for j, action in enumerate(history_item.model_output.action):
                    try:
                        # Get action details safely
                        action_type = None
                        action_params = {}

                        # Safe attribute access for action
                        if hasattr(action, 'model_fields_set') and action.model_fields_set:
                            for field_name in action.model_fields_set:
                                if hasattr(action, field_name):
                                    field_value = getattr(action, field_name)
                                    if field_value is not None:
                                        action_type = field_name
                                        action_params = field_value.model_dump() if hasattr(field_value, 'model_dump') else field_value
                                        break

                        if not action_type:
                            print(f"⚠️ Could not extract action type for step {i+1}, action {j+1}")
                            continue

                        # SIMPLE action data structure - just JSON, no complex DOM processing
                        action_data = {
                            "step_number": i + 1,
                            "action_type": action_type,
                            "action_params": action_params,
                            "llm_context": {
                                "evaluation_previous_goal": history_item.model_output.current_state.evaluation_previous_goal,
                                "memory": history_item.model_output.current_state.memory,
                                "next_goal": history_item.model_output.current_state.next_goal
                            }
                        }

                        successful_actions.append(action_data)
                        print(f"  ✅ Saved action {len(successful_actions)}: {action_type}")

                    except Exception as e:
                        print(f"⚠️ Error processing action {j+1} in step {i+1}: {e}")
                        continue

        if not successful_actions:
            print("⚠️ No successful actions found to save")
            return False

        # Create save data structure
        save_data = {
            "metadata": {
                "task": task,
                "execution_time": datetime.datetime.now().isoformat(),
                "total_steps": len(agent_history.history),
                "successful_steps": len(successful_actions),
                "execution_duration_seconds": execution_time,
                "agent_version": "1.0.0",
                "browser_config": {
                    "headless": False,
                    "window_size": [1280, 720]
                }
            },
            "successful_actions": successful_actions
        }

        # Create unique filename with date, time, and task name
        current_time = datetime.datetime.now()
        timestamp = current_time.strftime("%Y%m%d_%H%M%S")

        # # Clean task name for filename (remove invalid characters)
        # clean_task = "".join(c for c in task if c.isalnum() or c in (' ', '-', '_')).rstrip()
        # clean_task = clean_task.replace(' ', '_')[:50]  # Limit length and replace spaces

        # Create unique filename
        filename = f"execution_data_{timestamp}.json"
        output_path = f"outputs/{filename}"
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, indent=2, ensure_ascii=False)

        print(f"✅ Saved {len(successful_actions)} successful actions for rerun: {output_path}")
        print(f"   Task: {task}")
        print(f"   Duration: {execution_time} seconds")
        print(f"   Unique filename: {filename}")

        return True

    except Exception as e:
        print(f"❌ Error saving successful actions: {e}")
        return False
#----------------------------------------------------------------#

class MockLLM(BaseChatModel):
    """
    ==================== MockLLM - START ====================
    Mock LLM class that doesn't make actual API calls.
    Used for rerun functionality where we don't need LLM decisions.
    ==================== MockLLM - END ====================
    """

    # Define all attributes as proper Pydantic fields
    model_name: str = Field(default="mock-llm-for-rerun", description="Model name for MockLLM")
    call_count: int = Field(default=0, description="Number of times LLM was called")
    temperature: float  = Field(default=0.0, description="Temperature setting")
    max_tokens: int = Field(default=1000, description="Max tokens setting")
    saved_actions: list = Field(default_factory=list, description="Saved actions for rerun")
    current_action_index: int = Field(default=0, description="Current action index for rerun")
    skip_failures: bool = Field(default=False, description="Whether to skip failed actions or stop execution")
    agent_instance: object = Field(default=None, description="Reference to agent for state control")

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def _generate(self, messages, stop=None, run_manager=None, **kwargs):
        """Required method for BaseChatModel - sync generation"""
        self.call_count += 1
        print(f"⚠️ MockLLM._generate called (call #{self.call_count}) - This shouldn't happen in rerun!")

        from langchain_core.messages import AIMessage
        from langchain_core.outputs import ChatResult, ChatGeneration

        # Create mock AI response
        mock_message = AIMessage(content="Mock LLM response - rerun should not use this")
        generation = ChatGeneration(message=mock_message)
        return ChatResult(generations=[generation])

    async def _agenerate(self, messages, stop=None, run_manager=None, **kwargs):
        """Required method for BaseChatModel - async generation"""
        self.call_count += 1
        print(f"🤖 MockLLM._agenerate called (call #{self.call_count}) for rerun")

        # Return saved action if available
        if hasattr(self, 'saved_actions') and self.saved_actions and self.current_action_index < len(self.saved_actions):
            saved_action = self.saved_actions[self.current_action_index]
            print(f"   🎯 Returning saved action {self.current_action_index + 1}/{len(self.saved_actions)}: {saved_action['action_type']}")

            # Check if element can be found (simulate element validation)
            if not self._can_find_element_for_current_action(saved_action):
                if not self.skip_failures:
                    print("❌ Element not found, stopping agent execution")
                    # Stop the agent using its state control
                    if self.agent_instance:
                        self.agent_instance.state.stopped = True
                    # Return a "done" action to end execution
                    return self._create_done_response("Element not found - stopping execution")
                else:
                    print("⚠️ Element not found, skipping action")
                    self.current_action_index += 1
                    # Recursively try next action
                    return await self._agenerate(messages, stop, run_manager, **kwargs)

            # Use the actual LLM context from the saved data
            llm_context = saved_action.get('llm_context', {})

            # Create proper action format for browser_use using saved LLM context
            action_data = {
                "current_state": {
                    "evaluation_previous_goal": llm_context.get('evaluation_previous_goal', f"Rerun step {self.current_action_index + 1}"),
                    "memory": llm_context.get('memory', f"Executing saved action: {saved_action['action_type']}"),
                    "next_goal": llm_context.get('next_goal', f"Execute {saved_action['action_type']}")
                },
                "action": [{
                    saved_action["action_type"]: saved_action["action_params"]
                }]
            }

            print(f"   📝 LLM Context: {llm_context.get('evaluation_previous_goal', 'No evaluation')}")
            print(f"   🧠 Memory: {llm_context.get('memory', 'No memory')}")
            print(f"   🎯 Next Goal: {llm_context.get('next_goal', 'No goal')}")
            print(f"   🔧 Action Data: {action_data}")

            self.current_action_index += 1
            mock_message = AIMessage(content=json.dumps(action_data))
            generation = ChatGeneration(message=mock_message)
            return ChatResult(generations=[generation])
        else:
            # No more actions - signal completion
            print(f"   ✅ No more saved actions - signaling completion")
            completion_data = {
                "current_state": {
                    "evaluation_previous_goal": "All rerun actions completed",
                    "memory": "Rerun execution finished",
                    "next_goal": "Task completed"
                },
                "action": [{"done": {"text": "All saved actions executed successfully", "success": True}}]
            }

            print(f"   🏁 Completion Data: {completion_data}")
            mock_message = AIMessage(content=json.dumps(completion_data))
            generation = ChatGeneration(message=mock_message)
            return ChatResult(generations=[generation])

    def _can_find_element_for_current_action(self, saved_action):
        """Check if the current action's target element exists"""
        # Check if action has an index (targets an element)
        action_params = saved_action.get('action_params', {})

        if 'index' in action_params:
            target_index = action_params['index']
            print(f"   🔍 Validating element with index: {target_index}")

            # For now, simulate element validation
            # In a real implementation, you'd check if the element exists in current DOM
            # For testing, let's simulate that elements with index > 50 are "not found"
            if target_index > 50:
                print(f"   ❌ Element with index {target_index} not found (simulated)")
                return False
            else:
                print(f"   ✅ Element with index {target_index} found (simulated)")
                return True

        # No element targeting needed
        print(f"   ✅ Action doesn't target specific element")
        return True

    def _create_done_response(self, reason="Execution stopped"):
        """Create a response that signals the agent to stop with failure"""
        

        # Create a failed completion action
        done_action = {
            "current_state": {
                "evaluation_previous_goal": f"FAILED: {reason}",
                "memory": f"Rerun execution failed: {reason}",
                "next_goal": "Task failed - execution terminated"
            },
            "action": [{"done": {"text": f"FAILED: {reason}", "success": False}}]
        }

        print(f"   🛑 Creating FAILED done response: {reason}")

        # Also set agent state to indicate failure
        if self.agent_instance:
            # Mark the agent as having failed
            self.agent_instance.state.stopped = True
            # Add failure information to the last result
            from browser_use.agent.views import ActionResult
            failure_result = ActionResult(
                error=f"Rerun failed: {reason}",
                extracted_content=f"Element validation failed: {reason}",
                include_in_memory=True
            )
            self.agent_instance.state.last_result = [failure_result]

        content = json.dumps(done_action)
        mock_message = AIMessage(content=content)
        generation = ChatGeneration(message=mock_message)
        return ChatResult(generations=[generation])

    @property
    def _llm_type(self):
        return "mock"

    @property
    def _identifying_params(self):
        """Return identifying parameters for the LLM"""
        return {
            "model_name": self.model_name,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens
        }

    def with_structured_output(self, schema, **kwargs):
        """Override with_structured_output to handle agent's structured output calls"""
        print(f"🔧 MockLLM.with_structured_output called with schema: {schema}")

        class MockStructuredLLM:
            def __init__(self, mock_llm, schema):
                self.mock_llm = mock_llm
                self.schema = schema

            async def ainvoke(self, messages):
                """Handle structured output calls from agent"""
                print(f"🔧 MockStructuredLLM.ainvoke called")

                # Get the raw response from MockLLM
                result = await self.mock_llm._agenerate(messages)
                content = result.generations[0].message.content

                try:
                    import json
                    # Parse the JSON content
                    parsed_data = json.loads(content)
                    print(f"   ✅ Parsed JSON data: {parsed_data}")

                    # Create the schema instance
                    parsed_instance = self.schema(**parsed_data)
                    print(f"   ✅ Created schema instance: {type(parsed_instance)}")

                    return {
                        'parsed': parsed_instance,
                        'raw': result.generations[0].message
                    }
                except Exception as e:
                    print(f"   ❌ Failed to parse structured output: {e}")
                    print(f"   📝 Raw content: {content}")
                    return {
                        'parsed': None,
                        'raw': result.generations[0].message
                    }

        return MockStructuredLLM(self, schema)


def AuCreateMockLLM(skip_failures: bool = False) -> MockLLM:
    """
    ==================== AuCreateMockLLM - START ====================
    Create MockLLM instance for rerun functionality.

    Args:
        skip_failures: Whether to skip failed actions or stop execution

    Returns:
        MockLLM instance
    ==================== AuCreateMockLLM - END ====================
    """
    try:
        mock_llm = MockLLM()
        mock_llm.skip_failures = skip_failures
        print("✅ MockLLM created successfully")
        print("   - Will not make actual API calls")
        print("   - Designed for rerun functionality")
        print(f"   - Skip failures: {skip_failures}")
        return mock_llm

    except Exception as e:
        print(f"❌ Failed to create MockLLM: {e}")
        return None


async def AuCreateRerunAgent(task: str, browser: Browser, mock_llm: MockLLM = None) -> Agent:
    """
    ==================== AuCreateRerunAgent - START ====================
    Create Agent instance with MockLLM for rerun functionality.

    Args:
        task: Task description for the agent
        browser: Browser instance for the agent
        mock_llm: MockLLM instance (creates one if None)

    Returns:
        Agent instance or None if failed
    ==================== AuCreateRerunAgent - END ====================
    """
    try:
        # Create MockLLM if not provided
        if mock_llm is None:
            mock_llm = AuCreateMockLLM()
            if mock_llm is None:
                print("❌ Failed to create MockLLM for agent")
                return None

        print("🤖 Creating rerun agent...")
        print(f"   Task: {task}")
        print(f"   LLM: {mock_llm.model_name}")

        # Create agent with MockLLM (same pattern as working agent)
        agent = Agent(
            task=task,
            llm=mock_llm,
            browser=browser,
            max_actions_per_step=1,
            # Enable GIF generation for visual rerun representation
            generate_gif=os.path.join(output_folder, "rerun_history.gif")
        )

        # Pass agent reference to MockLLM for state control
        mock_llm.agent_instance = agent
        print("🔗 Agent reference passed to MockLLM for state control")

        print("✅ Rerun agent created successfully")
        return agent

    except Exception as e:
        print(f"❌ Failed to create rerun agent: {e}")
        return None



# ==================== END TASK 4: MOCKLLM & AGENT CREATION ====================

async def run_agent(task_text=None):
    global max_num_of_steps
    print(f"Execution Started...\nTask: {str(task_text)}")

    current_time = datetime.datetime.now()

    chrome_config = BrowserConfig(
        cdp_url="http://localhost:9222",  # Connect to running Chrome instance
        headless=False,  # Not relevant for an existing instance
        disable_security=True,
        extra_chromium_args=[]
    )

    context = """
Test Objectives:
1. Navigate to https://www.xbox.com/en-US/play
2. Attempt to launch the specified game
3. Validate game launch criteria:
   - Confirm video stream appears
   - Verify stream initialization

Test Constraints:
- Do NOT perform any in-game login or sign up
- Do NOT interact with game beyond initial launch
- Focus solely on stream verification

Success Criteria:
- video stream stream must successfully start
- Game stream must be visible on the platform
- The game should launch, but remain paused at the in-game account login or selection screen (if applicable)

Failure Conditions:
- No video/pixel stream appears
- Website fails to load game
- Stream does not initialize
"""

    if not enable_context: 
        context = ""

    agent = Agent(
        task=task_text + context,
        llm=AzureChatOpenAI(
            azure_deployment="gpt-4o",
            openai_api_version="2024-05-01-preview",
            azure_endpoint="https://michelangelovision.openai.azure.com/",
            api_key=os.getenv("AZURE_API_KEY"),
        ),
        browser=Browser(config=chrome_config),
        generate_gif=os.path.join(output_folder, "agent_history.gif"),
        max_actions_per_step=1,
        initial_actions= [{"go_to_url":{"url":"https://www.xbox.com/en-US/play"}},]
    )

    result = await agent.run(max_steps=max_num_of_steps)
    execution_time = (datetime.datetime.now() - current_time).seconds

    # 🎯 SAVE SUCCESSFUL ACTIONS FOR RERUN (only if task completed successfully)
    try:
        if result.is_done() and result.is_successful():
            print("🎉 Task completed successfully! Saving actions for rerun...")
            AuSaveSuccessfulActionsForRerun(result, task_text, execution_time)
        else:
            print("⚠️ Task not completed successfully - skipping save for rerun")
    except Exception as e:
        print(f"⚠️ Error saving for rerun: {e}")

    return result, execution_time

def execute_task(task_text):
    launch_chrome()
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    result, exec_time = loop.run_until_complete(run_agent(task_text))
    try:
        close_chrome()
    except Exception as e:
        pass
    parsed_data = parse_result(result, task_text, exec_time)
    print(f"**Execution Time:** {exec_time} seconds")
    try:
        modify_gif()
        return parsed_data, os.path.join(output_folder, "output.gif")
    except Exception as e:
        return parsed_data, os.path.join(output_folder, "agent_history.gif")

def execute_rerun(uploaded_file):
    """
    Execute rerun from uploaded saved execution file
    FOLLOWS EXACT SAME PATTERN AS execute_task()
    """
    try:
        if uploaded_file is None:
            return pd.DataFrame([["❌", "No file uploaded", "", ""]],
                              columns=["Status", "Action", "Result", "Time"]), None

        print(f"🔄 Starting rerun from file: {uploaded_file.name}")

        # Launch Chrome for rerun (SAME AS INITIAL)
        launch_chrome()

        # Create new event loop for rerun (SAME AS INITIAL)
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # Execute rerun (SAME PATTERN AS INITIAL)
        # Set skip_failures=False to test breaking mechanism
        result, exec_time = loop.run_until_complete(run_rerun_agent(uploaded_file.name, skip_failures=False))

        # Close Chrome (SAME AS INITIAL)
        try:
            close_chrome()
        except Exception as e:
            pass

        # Parse result (SAME AS INITIAL)
        parsed_data = parse_result(result, "Rerun saved actions", exec_time)
        print(f"**Rerun Execution Time:** {exec_time} seconds")

        # Try to create GIF (SAME AS INITIAL)
        try:
            modify_rerun_gif()
            return parsed_data, os.path.join(output_folder, "rerun_output.gif")
        except Exception as e:
            return parsed_data, os.path.join(output_folder, "rerun_history.gif")

    except Exception as e:
        print(f"❌ Rerun execution failed: {e}")
        try:
            close_chrome()
        except:
            pass
        return pd.DataFrame([["❌", "Rerun Failed", str(e), ""]],
                          columns=["Status", "Action", "Result", "Time"]), None

async def run_rerun_agent(saved_file_path, skip_failures: bool = False):
    """
    Run rerun agent - FOLLOWS EXACT SAME PATTERN AS run_agent()
    """
    print(f"🔄 Rerun Execution Started...\nSaved File: {saved_file_path}")

    current_time = datetime.datetime.now()

    # Load saved actions first
    saved_actions = AuLoadExecutionDataForRerun(saved_file_path)
    if not saved_actions:
        raise Exception("No saved actions found")

    print(f"📁 Loaded {len(saved_actions)} saved actions")

    # Create browser config (SAME AS INITIAL)
    chrome_config = BrowserConfig(
        cdp_url="http://localhost:9222",  # Connect to running Chrome instance
        headless=False,  # Not relevant for an existing instance
        disable_security=True,
        extra_chromium_args=[]
    )

    # Create MockLLM (INSTEAD OF REAL LLM)
    mock_llm = AuCreateMockLLM(skip_failures=skip_failures)
    if not mock_llm:
        raise Exception("Failed to create MockLLM")

    # Configure MockLLM with saved actions
    mock_llm.saved_actions = saved_actions
    mock_llm.current_action_index = 0
    print(f"🤖 MockLLM configured with {len(saved_actions)} saved actions")
    print(f"   🔧 Skip failures: {skip_failures}")

    # Create agent (SAME PATTERN AS INITIAL)
    agent = Agent(
        task="Rerun saved actions",  # Simple task for rerun
        llm=mock_llm,  # MockLLM instead of real LLM
        browser=Browser(config=chrome_config),
        generate_gif=os.path.join(output_folder, "rerun_history.gif"),
        max_actions_per_step=1,
        # No initial_actions for rerun - start from saved actions
    )

    # Pass agent reference to MockLLM for state control
    mock_llm.agent_instance = agent
    print("🔗 Agent reference passed to MockLLM for state control")

    print("🎬 Starting rerun with visual browser actions...")

    try:
        # Run agent (SAME AS INITIAL)
        result = await agent.run(max_steps=len(saved_actions) + 2)  # Buffer for completion
        execution_time = (datetime.datetime.now() - current_time).seconds

        # Check if all expected steps were completed
        expected_steps = len(saved_actions)
        actual_steps = len(result.history) if hasattr(result, 'history') else 0

        print(f"🎯 Rerun completed in {execution_time} seconds")
        print(f"📊 Steps: {actual_steps}/{expected_steps} completed")

        # Check for action errors in the history
        if hasattr(result, 'history') and result.history:
            for i, history_item in enumerate(result.history):
                if hasattr(history_item, 'result') and history_item.result:
                    for action_result in history_item.result:
                        if hasattr(action_result, 'error') and action_result.error:
                            step_num = i + 1
                            print(f"❌ Rerun failed at step {step_num}: {action_result.error}")

                            # Create a failed result object using unified class
                            failed_result = RerunFailedResult(
                                partial_history=result.history[:i+1],  # Include steps up to the failure
                                error_msg=f"Step {step_num} failed: {action_result.error}",
                                failed_step=step_num
                            )
                            return failed_result, execution_time

        # Check if rerun failed due to incomplete execution
        if actual_steps < expected_steps:
            print(f"⚠️ Rerun incomplete: Only {actual_steps} of {expected_steps} steps executed")
            # Create a failed result object using unified class
            failed_result = RerunFailedResult(
                partial_history=result.history if hasattr(result, 'history') else [],
                error_msg=f"Rerun failed: Only {actual_steps} of {expected_steps} steps completed"
            )
            return failed_result, execution_time

        # Return successful result (SAME AS INITIAL)
        return result, execution_time

    except Exception as e:
        execution_time = (datetime.datetime.now() - current_time).seconds
        print(f"❌ Rerun execution failed: {e}")

        # Create a failed result object using unified class
        failed_result = RerunFailedResult(
            partial_history=[],
            error_msg=str(e)
        )
        return failed_result, execution_time


def parse_result(result, task_text, exec_time):
    # Check if this is a failed rerun result due to element validation
    agent_stopped_due_to_failure = False
    failure_reason = "Unknown failure"

    # Check if agent was stopped and has failure information
    if hasattr(result, 'history') and result.history:
        # Check the last history item for failure indicators
        last_history = result.history[-1] if result.history else None
        if last_history and hasattr(last_history, 'result') and last_history.result:
            for action_result in last_history.result:
                if hasattr(action_result, 'error') and action_result.error:
                    if "Element not found" in action_result.error or "Rerun failed" in action_result.error:
                        agent_stopped_due_to_failure = True
                        failure_reason = action_result.error
                        break

        # Also check if the last action was a failed "done" action
        if last_history and hasattr(last_history, 'model_output') and last_history.model_output:
            if hasattr(last_history.model_output, 'action') and last_history.model_output.action:
                for action in last_history.model_output.action:
                    if hasattr(action, 'done') and action.done:
                        done_text = getattr(action.done, 'text', '')
                        if "FAILED:" in done_text:
                            agent_stopped_due_to_failure = True
                            failure_reason = done_text
                            break

    # If agent stopped due to failure, treat as failed rerun
    if agent_stopped_due_to_failure:
        print(f"❌ Detected rerun failure: {failure_reason}")
        # Create a mock failed result object using unified class
        result = RerunFailedResult(
            partial_history=result.history if hasattr(result, 'history') else [],
            error_msg=failure_reason
        )

    # Check if this is a failed rerun result
    if hasattr(result, '_is_failed') and result._is_failed:
        print(f"❌ Parsing failed rerun result: {result._failure_reason}")

        # Parse the partial history to show detailed steps
        action_list = []
        test_report_data = []

        if hasattr(result, 'history') and result.history:
            for i, history in enumerate(result.history):
                try:
                    if "model_output" in history.model_fields_set and history.model_output.model_fields_set and "action" in history.model_output.model_fields_set:
                        for action in history.model_output.action:
                            for step in action.model_fields_set:
                                action_step = getattr(action, step)

                                # Get action details
                                action_name = str(action_step.__class__.__name__).removesuffix("Action")
                                goal = getattr(history.model_output.current_state, 'next_goal', 'Execute saved action')

                                # Check if this step had an error
                                evaluation = "Success"
                                result_text = "Step completed"

                                if hasattr(history, 'result') and history.result:
                                    for action_result in history.result:
                                        if hasattr(action_result, 'error') and action_result.error:
                                            evaluation = "Failed"
                                            result_text = action_result.error
                                            break
                                        elif hasattr(action_result, 'extracted_content') and action_result.extracted_content:
                                            result_text = action_result.extracted_content

                                # Add to detailed steps
                                test_report_data.append({
                                    "Action": action_name,
                                    "Goal": goal,
                                    "evaluation": evaluation,
                                    "result": result_text
                                })

                                # Add to action list for dataframe
                                action_list.append({
                                    "Action": action_name,
                                    "Goal": goal,
                                    "XPath": "",
                                    "Value": ""
                                })
                except Exception as e:
                    print(f"Error parsing step {i}: {e}")
                    pass

        # If no steps were parsed, add a generic failed step
        if not test_report_data:
            test_report_data.append({
                "Action": "Rerun",
                "Goal": "Execute saved actions",
                "evaluation": "Failed",
                "result": result._failure_reason
            })

        # Create detailed failed report
        test_report = {
            "action_list": test_report_data,
            "task": task_text,
            "execution_time": str(exec_time) + "seconds",
            "final_result_summary": result._failure_reason,
            "status": False,  # FAIL status
            "current_time" : datetime.datetime.now().strftime("%m/%d/%Y, %I:%M:%S %p").lstrip("0").replace("/0", "/")
        }

        try:
            update_html_report(test_report)
            open_pdf()
        except Exception as e:
            print(f"Error generating failed report: {e}")

        # Return detailed dataframe for failed rerun
        if action_list:
            return pd.DataFrame(action_list)
        else:
            return pd.DataFrame([["❌", "Rerun Failed", result._failure_reason, exec_time]],
                              columns=["Status", "Action", "Result", "Time"])

    action_list = []
    for history in result.history:
        try:
            if "model_output" in history.model_fields_set and history.model_output.model_fields_set and "action" in history.model_output.model_fields_set:
                action_dict = None
                try:
                    for index, action in enumerate(history.model_output.action):
                        try:
                            for step in action.model_fields_set:
                                action_step = getattr(action, step)
                                xpath = None
                                if hasattr(action_step, "index"):
                                    try:
                                        for interacted_element in history.state.interacted_element:
                                            if interacted_element.highlight_index == action_step.index:
                                                xpath = interacted_element.xpath
                                    except Exception as e:
                                        pass
                                if action_dict is None:
                                    try:
                                        action_dict = {"action": action_step, "xpath": xpath, "goal": history.model_output.current_state.next_goal}
                                    except Exception as e:
                                        action_dict = {"action": action_step, "xpath": xpath}
                                else:
                                    action_dict = {"action": action_step, "xpath": xpath}
                                try:
                                    action_dict["evaluation_previous_goal"] = history.model_output.current_state.evaluation_previous_goal
                                except Exception as e:
                                    pass
                                action_list.append(action_dict)
                        except Exception as e:
                            pass
                except Exception as e:
                    pass
        except Exception as e:
            pass
                    
    try:
        final_result = result.history[-1].result[0]
    except Exception as e:
        final_result = ""

    return parse_and_display_result(action_list, task_text, exec_time, final_result)

def parse_and_display_result(result, task_text, exec_time, final_result):
    parsed_data = []
    test_report_data = []

    prev_item = None
    for item in result:
        try:
            if hasattr(item["action"], "url"):
                value = item["action"].url
            elif hasattr(item["action"], "text"):
                value = item["action"].text
            else:
                value = ""
            xpath = item.get("xpath", "")
            goal = item.get("goal", "")
            evaluation_previous_goal = item.get("evaluation_previous_goal", "")
            xpath = xpath if xpath is not None else ""
            goal = goal if goal is not None else ""
            if prev_item is not None:
                if 'Success' in evaluation_previous_goal:
                    prev_item["evaluation"] = "Success"
                    prev_item["result"] = evaluation_previous_goal.strip().replace("Success - ", "").strip()
                elif 'Failed' in evaluation_previous_goal:
                    prev_item["evaluation"] = "Failed"
                    prev_item["result"] = evaluation_previous_goal.strip().replace("Failed - ", "").strip()
                else:
                    prev_item["evaluation"] = "Unknown"
                    prev_item["result"] = evaluation_previous_goal.strip()
                test_report_data.append(prev_item)
            prev_item = {"Action": str(item["action"].__class__.__name__).removesuffix("Action"), "Goal": goal}
            parsed_data.append({"Action": str(item["action"].__class__.__name__).removesuffix("Action"), "Goal": goal, "XPath": xpath, "Value": value})
        except Exception as e:
            pass
    status = None
    if isinstance(final_result, str):
        final_result_summary = final_result
    elif hasattr(final_result, "extracted_content"):
        final_result_summary = str(final_result.extracted_content)
    else:
        final_result_summary = ""
    if prev_item is not None:
        if final_result.is_done:
            status = final_result.success

            # Check if this was a failed rerun (agent stopped due to element validation failure)
            if hasattr(final_result, 'extracted_content') and final_result.extracted_content:
                content = str(final_result.extracted_content)
                if "FAILED:" in content or "Element not found" in content or "Rerun failed" in content:
                    status = False  # Override to failed status

            # Also check if there's an error in the final result
            if hasattr(final_result, 'error') and final_result.error:
                if "Element not found" in final_result.error or "Rerun failed" in final_result.error:
                    status = False  # Override to failed status

            if status:
                prev_item["evaluation"] = "Success"
                prev_item["result"] = "Task completed successfully"
            else:
                prev_item["evaluation"] = "Failed"
                prev_item["result"] = "Task execution was not successful"
        else:
            pass
        test_report_data.append(prev_item)
    test_report = {
        "action_list": test_report_data, 
        "task": task_text, 
        "execution_time": str(exec_time) + "seconds", 
        "final_result_summary": final_result_summary, 
        "status": status, 
        "current_time" : datetime.datetime.now().strftime("%m/%d/%Y, %I:%M:%S %p").lstrip("0").replace("/0", "/")
    }
    try:
        update_html_report(test_report)
        open_pdf()
    except Exception as e:
        print(f"Error:{e}")
    if parsed_data:
        print("Data Parsed.")
        return pd.DataFrame(parsed_data)
    else:
        print(parsed_data)
        print(result)
        return empty_df
    
def update_html_report(report_data, input_path="./assets/html/test_report.html", output_path=os.path.join(output_folder, "updated_test_report.html")):
    with sync_playwright() as p:
        try:
            CHROME_PATH = "C:/Program Files/Google/Chrome/Application/chrome.exe" 
            browser = p.chromium.launch(executable_path=CHROME_PATH, headless=True)
            print("Headless Chrome Loaded...")
        except Exception as e:
            print(f"Error:{e}")
            print("Using Existing Chrome Browser...")
            close_chrome()
            launch_chrome()
            browser = p.chromium.connect_over_cdp("http://localhost:9222")
        page = browser.new_page()

        with open(input_path, "r", encoding="utf-8") as f:
            content = f.read()

        page.set_content(content)

        try:
            page.evaluate('document.querySelector(".container p:nth-child(2)").innerHTML = "<strong>Task:</strong>' + str(report_data["task"]).split(":")[0] +'";')
        except Exception as e:
            pass
        try:
            page.evaluate(f'''
    const statusElement = document.querySelector(".summary p:nth-child(1) span");
    statusElement.innerHTML = "{'Success' if report_data["status"] else 'Failed'}";
    statusElement.className = "{'status-success' if report_data["status"] else 'status-fail'}";
''')
        except Exception as e:
            pass
        try:
            page.evaluate(f'document.querySelector(".summary p:nth-child(2)").innerHTML = "<strong>Execution Time:</strong> {report_data["execution_time"]}";')
        except Exception as e:
            pass
        try:
            page.evaluate(f'document.querySelector(".summary p:nth-child(3)").innerHTML = "<strong>Final Result:</strong> {report_data["final_result_summary"]}";')
        except Exception as e:
            try:
                print(report_data["final_result_summary"])
            except Exception as e:
                pass

        page.evaluate("document.querySelector('tbody').innerHTML = '';")

        rows_html = ""
        for i, action in enumerate(report_data["action_list"], start=1):
            rows_html += f"""
            <tr>
                <td>{i}</td>
                <td>{action['Action']}</td>
                <td>{action['Goal']}</td>
                <td class="{'status-success' if action['evaluation'] == 'Success' else 'status-fail'}">{action['evaluation']}</td>
                <td>{action['result']}</td>
            </tr>
            """

        try:
            page.evaluate(f"document.querySelector('tbody').innerHTML = `{rows_html}`;")
        except Exception as e:
            pass

        try:
            page.evaluate(f'document.querySelector(".footer p strong").innerText = "{report_data["current_time"]}";')
        except Exception as e:
            pass

        updated_content = page.content()

        page.pdf(path=os.path.join(output_folder, "test_case_report.pdf"))

        with open(output_path, "w", encoding="utf-8") as f:
            f.write(updated_content)

        # static_folder = "static"
        # if not os.path.exists(static_folder):
        #     os.makedirs(static_folder)
        
        # # Example HTML content (replace this with actual content)
        # file_path = os.path.join(static_folder, "updated_test_report.html")
        # with open(file_path, 'w') as f:
        #     f.write(updated_content)

        browser.close()


custom_css = """
<style>
.fixed-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #1e1e1e; /* Optional: Match background theme */
    padding: 10px 20px;
    z-index: 999;
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.5);
}
.task-box {
    width: 100%;
    margin-bottom: 10px;
}
</style>
"""

with gr.Blocks(css=".container { max-width: 900px !important; }") as demo:

    gr.HTML(
"""
<head>
    <title>Test Automation</title>
</head>
<body>
    <div style="text-align: center; padding: 10px;">
        <h2>Test Automation</h2>
        <p style="color: #555;">A simple UI to execute tasks and upload files with AI assistance</p>
    </div>
</body>
"""
    )
    with gr.Row():
        with gr.Column():
            gr.Markdown("### 📋 Test Script")
            test_script = gr.DataFrame(value=empty_df, interactive=False)

        with gr.Column():
            gr.Markdown("### 📸 Preview Image")
            preview_image = gr.Image(preview_image_path, label="Preview")

    with gr.Row(elem_classes="fixed-bottom"):
        task_input = gr.Textbox(
            label="",
            placeholder="Ask anything or describe the task...",
            interactive=True,
            container=False,
            elem_classes="task-box"
        )

    with gr.Row(elem_classes="fixed-bottom", scale=0):
        execute_btn = gr.Button("Execute Task")
        save_btn = gr.Button("💾 Save Test Case")

    # Rerun functionality section
    with gr.Row():
        with gr.Column():
            gr.Markdown("### 🔄 Rerun Saved Execution")
            rerun_file = gr.File(
                label="Upload Saved Execution File",
                file_types=[".json"],
                type="filepath"
            )
        with gr.Column():
            gr.Markdown("### 📊 Rerun Status")
            rerun_btn = gr.Button("🚀 Execute Rerun", variant="secondary")

    gr.HTML("<a href='/static/updated_test_report.html' target='_blank'>View Test Report</a>")

    # Original task execution
    execute_btn.click(
        fn=execute_task,
        inputs=task_input,
        outputs=[test_script, preview_image]
    )

    # Rerun execution
    rerun_btn.click(
        fn=execute_rerun,
        inputs=rerun_file,
        outputs=[test_script, preview_image]
    )

    # def get_report_link(task_output):
    #     return f"<a href='{task_output[1]}' target='_blank'>View Test Report</a>"

    # # Bind the function to the 'report_link'
    # report_link.change(fn=get_report_link, inputs=execute_btn, outputs=report_link)

demo.launch(inbrowser=load_in_browser)
