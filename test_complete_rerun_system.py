#!/usr/bin/env python3
"""
Test complete rerun system with proper MockLLM that executes ALL saved actions
"""

import sys
import os
import json
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_saved_data_structure():
    print("🧪 Testing saved data structure...")
    
    try:
        # Load current saved data
        with open('outputs/execution_data_for_rerun.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        actions = data.get('successful_actions', [])
        print(f"📊 Found {len(actions)} saved actions")
        
        # Analyze structure
        for i, action in enumerate(actions):
            step_num = action.get('step_number', i+1)
            action_type = action.get('action_type', 'unknown')
            action_params = action.get('action_params', {})
            original_index = action.get('original_index')
            
            print(f"   Step {step_num}: {action_type}")
            if original_index is not None:
                print(f"     Index: {original_index}")
            print(f"     Params: {action_params}")
            
        return len(actions) > 0
        
    except Exception as e:
        print(f"❌ Error loading saved data: {e}")
        return False

async def test_complete_rerun_execution():
    print("\n🧪 Testing complete rerun execution...")
    
    try:
        # Import required components
        from app import AuLoadExecutionDataForRerun, AuCreateMockLLM, AuCreateRerunAgent
        from browser_use.browser.browser import Browser
        from browser_use.browser.context import BrowserContext
        
        print("✅ Imports successful")
        
        # 1. Load saved actions
        saved_actions = AuLoadExecutionDataForRerun('outputs/execution_data_for_rerun.json')
        print(f"✅ Loaded {len(saved_actions)} saved actions")
        
        # 2. Create MockLLM with ALL saved actions
        mock_llm = AuCreateMockLLM(saved_actions)
        print(f"✅ Created MockLLM with {len(saved_actions)} actions")
        
        # 3. Create browser and agent
        browser = Browser()
        browser_context = await browser.new_context()
        agent = AuCreateRerunAgent(mock_llm, browser_context)
        print("✅ Created browser and agent")
        
        # 4. Execute rerun with ALL actions
        print(f"🎬 Starting rerun execution with {len(saved_actions)} actions...")
        
        # Set max_steps to accommodate all saved actions + buffer
        max_steps = len(saved_actions) + 2
        result = await agent.run(max_steps=max_steps)
        
        print(f"✅ Rerun completed")
        print(f"   Steps executed: {len(result.history) if hasattr(result, 'history') else 'Unknown'}")
        print(f"   Task done: {result.is_done() if hasattr(result, 'is_done') else 'Unknown'}")
        print(f"   Task successful: {result.is_successful() if hasattr(result, 'is_successful') else 'Unknown'}")
        
        # 5. Cleanup
        await browser.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Rerun execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_index_based_execution():
    print("\n🧪 Testing index-based execution logic...")
    
    # Simulate the expected execution flow
    expected_flow = [
        {"step": 1, "action": "open_tab", "index": None, "should_work": True},
        {"step": 2, "action": "input_text", "index": 3, "should_work": True},
        {"step": 3, "action": "click_element", "index": 8, "should_work": True},
        {"step": 4, "action": "click_element", "index": 30, "should_work": True},
        {"step": 5, "action": "done", "index": None, "should_work": True}
    ]
    
    print("📋 Expected execution flow:")
    for step_info in expected_flow:
        step = step_info["step"]
        action = step_info["action"]
        index = step_info["index"]
        
        if index is not None:
            print(f"   Step {step}: {action} with index {index}")
            print(f"     → Agent will try to use index {index}")
            print(f"     → If index {index} not found → FAIL TEST immediately")
        else:
            print(f"   Step {step}: {action} (no index needed)")
            
    print("\n🎯 Key points:")
    print("   - Each step = 1 action")
    print("   - Use original index from saved data")
    print("   - If index fails → fail test with done action")
    print("   - No xpath fallback for regression testing")
    
    return True

async def main():
    print("🧪 Complete Rerun System Test")
    print("=" * 60)
    
    # Test 1: Verify saved data structure
    success1 = test_saved_data_structure()
    
    # Test 2: Test index-based execution logic
    success2 = test_index_based_execution()
    
    # Test 3: Test complete rerun execution
    success3 = await test_complete_rerun_execution()
    
    print(f"\n🎯 RESULTS:")
    print(f"   Saved data structure: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"   Index-based logic: {'✅ PASS' if success2 else '❌ FAIL'}")
    print(f"   Complete rerun execution: {'✅ PASS' if success3 else '❌ FAIL'}")
    
    if success1 and success2 and success3:
        print("\n🎉 ALL TESTS PASSED! Complete rerun system working!")
        return 0
    else:
        print("\n⚠️ Some tests failed. Need to fix issues.")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
