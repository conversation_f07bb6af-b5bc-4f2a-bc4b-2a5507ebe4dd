<!DOCTYPE html><html><head>
    <meta charset="UTF-8">
    <title>Test Case Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f8f9fa;
            margin: 40px;
        }
        .container {
            max-width: 900px;
            margin: auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #343a40;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f1f1f1;
        }
        .status-success {
            color: #28a745;
            font-weight: bold;
        }
        .status-fail {
            color: #dc3545;
            font-weight: bold;
        }
        .summary {
            margin-top: 20px;
            padding: 10px;
            background-color: #e9ecef;
            border-left: 5px solid #17a2b8;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            font-size: 14px;
            color: #6c757d;
        }
    </style>
<script type="text/javascript" nonce="8pPBpL2VDBQxsZ2O61afaw" src="https://mail-attachment.googleusercontent.com/j-ZybvmO3lut028p5zvYCvf50dkAbsuyfPHXTAL2jE6TNBo0qDIqkCUDN3ItV5ZpIRamuKivmVb3G9tZl0yXOg=="></script></head>
<body>

<div class="container">
    <h1>📄 Test Case Report</h1>
    <p><strong>Task:</strong>Rerun saved actions</p>

    <h2>✅ Task Summary</h2>
    <div class="summary">
        <p><strong>Status:</strong> <span class="status-fail">Failed</span></p>
        <p><strong>Execution Time:</strong> 21seconds</p>
        <p><strong>Final Result:</strong> Rerun failed: Only 6 of 11 steps completed</p>
    </div>

    <h2>📚 Detailed Steps</h2>
    <table>
        <thead>
            <tr>
                <th>Step</th>
                <th>Action</th>
                <th>Goal</th>
                <th>Evaluation</th>
                <th>Result</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>1</td>
                <td>OpenTab</td>
                <td>Open a new tab and navigate to Google's homepage.</td>
                <td class="status-success">Success</td>
                <td>🔗  Opened new tab with https://www.google.com</td>
            </tr>
            
            <tr>
                <td>2</td>
                <td>InputText</td>
                <td>Search for YouTube on Google.</td>
                <td class="status-success">Success</td>
                <td>⌨️  Input YouTube into index 6</td>
            </tr>
            
            <tr>
                <td>3</td>
                <td>ClickElement</td>
                <td>Click on the YouTube suggestion to navigate to YouTube.</td>
                <td class="status-success">Success</td>
                <td>🖱️  Clicked button with index 12: </td>
            </tr>
            
            <tr>
                <td>4</td>
                <td>ClickElement</td>
                <td>Click on the link for YouTube to open the site.</td>
                <td class="status-success">Success</td>
                <td>🖱️  Clicked button with index 18: YouTube</td>
            </tr>
            
            <tr>
                <td>5</td>
                <td>InputText</td>
                <td>Input 'maand song' in the YouTube search bar and perform the search.</td>
                <td class="status-success">Success</td>
                <td>⌨️  Input maand song into index 3</td>
            </tr>
            
            <tr>
                <td>6</td>
                <td>ClickElement</td>
                <td>Click the search button to find 'maand song' on YouTube.</td>
                <td class="status-success">Success</td>
                <td>🖱️  Clicked button with index 5: </td>
            </tr>
            </tbody>
    </table>

    <div class="footer">
        <p>📅 Report generated on <strong>7/22/2025, 06:40:36 PM</strong></p>
    </div>
</div>



</body></html>