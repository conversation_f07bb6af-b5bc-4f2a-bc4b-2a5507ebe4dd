#!/usr/bin/env python3
"""
Test the simplified index-based rerun approach
"""

import sys
import os
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_index_based_save_logic():
    print("🧪 Testing simplified index-based save logic...")
    
    # Simulate the new save logic
    test_actions = [
        {
            "action_type": "open_tab",
            "action_params": {"url": "https://www.youtube.com"},
            "has_index": False
        },
        {
            "action_type": "input_text", 
            "action_params": {"index": 3, "text": "2002 song by <PERSON><PERSON><PERSON>"},
            "has_index": True
        },
        {
            "action_type": "click_element",
            "action_params": {"index": 8},
            "has_index": True
        },
        {
            "action_type": "click_element",
            "action_params": {"index": 30},
            "has_index": True
        },
        {
            "action_type": "done",
            "action_params": {"text": "Task completed", "success": True},
            "has_index": False
        }
    ]
    
    saved_actions = []
    
    for i, test_action in enumerate(test_actions):
        action_type = test_action["action_type"]
        action_params = test_action["action_params"]
        
        # Apply the new simplified save logic
        action_data = {
            "step_number": i + 1,
            "action_index": 0,
            "action_type": action_type,
            "action_params": action_params,
            "xpath": None,  # Simplified - no complex DOM processing
        }
        
        # Extract index from action params for rerun (NEW LOGIC)
        if isinstance(action_params, dict) and "index" in action_params:
            action_data["original_index"] = action_params["index"]
        else:
            action_data["original_index"] = None
            
        # Add minimal element info for debugging (NEW LOGIC)
        action_data["element_info"] = {
            "original_index": action_data["original_index"],
            "action_requires_element": action_type in ["click_element", "input_text", "select_option"],
            "xpath_fallback_available": False
        }
        
        saved_actions.append(action_data)
        
        # Show what would be saved
        if action_data["original_index"] is not None:
            print(f"   ✅ Action {i+1} ({action_type}): Index {action_data['original_index']} - READY FOR INDEX-BASED RERUN")
        else:
            print(f"   ✅ Action {i+1} ({action_type}): No index needed - READY FOR DIRECT RERUN")
    
    print(f"\n📊 Results:")
    print(f"   Total actions: {len(saved_actions)}")
    index_actions = [a for a in saved_actions if a["original_index"] is not None]
    print(f"   Index-based actions: {len(index_actions)}")
    print(f"   Direct actions: {len(saved_actions) - len(index_actions)}")
    
    return len(saved_actions) == 5  # Should save all 5 actions

def test_rerun_execution_logic():
    print("\n🧪 Testing rerun execution logic...")
    
    # Simulate saved action data
    saved_action = {
        "action_type": "input_text",
        "action_params": {"index": 3, "text": "2002 song by Anne-Marie"},
        "original_index": 3,
        "element_info": {
            "original_index": 3,
            "action_requires_element": True,
            "xpath_fallback_available": False
        }
    }
    
    print(f"📋 Saved action: {saved_action['action_type']}")
    print(f"   Original index: {saved_action['original_index']}")
    print(f"   Action params: {saved_action['action_params']}")
    
    # Simulate MockLLM returning this action
    action_data = {
        "current_state": {
            "evaluation_previous_goal": "Success - Previous step completed",
            "memory": "Executing saved action",
            "next_goal": "Input text into search field"
        },
        "action": [{
            saved_action["action_type"]: saved_action["action_params"]
        }]
    }
    
    print(f"🤖 MockLLM would return:")
    print(f"   Action: {action_data['action']}")
    print(f"   Index in params: {saved_action['action_params'].get('index')}")
    
    # The browser_use agent would receive this and use index 3 directly
    print(f"✅ Agent would execute: {saved_action['action_type']} with index {saved_action['action_params']['index']}")
    print(f"✅ No complex DOM processing needed!")
    print(f"✅ If index 3 fails, agent can implement fallback logic")
    
    return True

if __name__ == "__main__":
    print("🧪 Testing Index-Based Rerun Approach")
    print("=" * 60)
    
    success1 = test_index_based_save_logic()
    success2 = test_rerun_execution_logic()
    
    if success1 and success2:
        print("\n🎉 All tests passed! Index-based rerun should work perfectly.")
        print("\n🎯 Expected improvements:")
        print("   - All 5 actions saved (no DOM processing errors)")
        print("   - Rerun uses original indices directly")
        print("   - Much simpler and more reliable")
        print("   - Perfect for regression testing")
        exit(0)
    else:
        print("\n❌ Some tests failed.")
        exit(1)
