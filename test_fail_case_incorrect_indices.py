#!/usr/bin/env python3
"""
Test case demonstrating how rerun fails when indices are incorrect
"""

import sys
import os
import json
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_fail_test_data():
    print("🧪 Analyzing FAIL TEST data with incorrect indices...")
    
    try:
        # Load the fail test data
        with open('outputs/execution_data_for_rerun_FAIL_TEST.json', 'r', encoding='utf-8') as f:
            fail_data = json.load(f)
            
        # Load the original working data for comparison
        with open('outputs/execution_data_for_rerun.json', 'r', encoding='utf-8') as f:
            working_data = json.load(f)
            
        fail_actions = fail_data.get('successful_actions', [])
        working_actions = working_data.get('successful_actions', [])
        
        print(f"📊 Comparison:")
        print(f"   Working data: {len(working_actions)} actions")
        print(f"   Fail test data: {len(fail_actions)} actions")
        
        print(f"\n🔍 Index Comparison:")
        print(f"{'Step':<6} {'Action':<15} {'Working Index':<15} {'Fail Index':<12} {'Expected Result'}")
        print("=" * 80)
        
        for i, (working, fail) in enumerate(zip(working_actions, fail_actions)):
            step = working.get('step_number', i+1)
            action_type = working.get('action_type', 'unknown')
            
            working_index = working.get('action_params', {}).get('index', 'N/A')
            fail_index = fail.get('action_params', {}).get('index', 'N/A')
            
            if action_type in ['input_text', 'click_element']:
                if working_index != fail_index:
                    result = "❌ WILL FAIL"
                else:
                    result = "✅ Should work"
            else:
                result = "✅ No index needed"
                
            print(f"{step:<6} {action_type:<15} {working_index:<15} {fail_index:<12} {result}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing data: {e}")
        return False

def simulate_rerun_failure():
    print("\n🧪 Simulating rerun execution with incorrect indices...")
    
    try:
        # Load fail test data
        with open('outputs/execution_data_for_rerun_FAIL_TEST.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        actions = data.get('successful_actions', [])
        
        print(f"🎬 Simulating rerun execution:")
        print(f"   Task: {data['metadata']['task']}")
        print(f"   Actions to execute: {len(actions)}")
        
        for i, action in enumerate(actions):
            step = action.get('step_number', i+1)
            action_type = action.get('action_type')
            action_params = action.get('action_params', {})
            index = action_params.get('index')
            
            print(f"\n   Step {step}: {action_type}")
            
            if action_type == "open_tab":
                url = action_params.get('url')
                print(f"     → Opening {url}")
                print(f"     → ✅ SUCCESS (no index needed)")
                
            elif action_type == "input_text":
                text = action_params.get('text')
                print(f"     → Trying to input '{text}' at index {index}")
                print(f"     → ❌ FAIL: Index {index} not found on page!")
                print(f"     → 🚨 REGRESSION TEST FAILED at step {step}")
                print(f"     → 🛑 Stopping execution - test case failed")
                return False
                
            elif action_type == "click_element":
                print(f"     → Trying to click element at index {index}")
                print(f"     → ❌ FAIL: Index {index} not found on page!")
                print(f"     → 🚨 REGRESSION TEST FAILED at step {step}")
                print(f"     → 🛑 Stopping execution - test case failed")
                return False
                
            elif action_type == "done":
                print(f"     → Task completion")
                print(f"     → ⚠️ This should never be reached due to earlier failures")
                
        return True
        
    except Exception as e:
        print(f"❌ Error simulating rerun: {e}")
        return False

async def test_actual_rerun_failure():
    print("\n🧪 Testing actual rerun execution with fail data...")
    
    try:
        # Import required components
        from app import AuLoadExecutionDataForRerun, AuCreateMockLLM, AuCreateRerunAgent
        from browser_use.browser.browser import Browser
        from browser_use.browser.context import BrowserContext
        
        print("✅ Imports successful")
        
        # 1. Load fail test data
        saved_actions = AuLoadExecutionDataForRerun('outputs/execution_data_for_rerun_FAIL_TEST.json')
        print(f"✅ Loaded {len(saved_actions)} saved actions (with incorrect indices)")
        
        # 2. Create MockLLM with fail data
        mock_llm = AuCreateMockLLM(saved_actions)
        print(f"✅ Created MockLLM with {len(saved_actions)} actions")
        
        # 3. Create browser and agent
        browser = Browser()
        browser_context = await browser.new_context()
        agent = AuCreateRerunAgent(mock_llm, browser_context)
        print("✅ Created browser and agent")
        
        # 4. Execute rerun (should fail)
        print(f"🎬 Starting rerun execution (expecting failure)...")
        
        try:
            result = await agent.run(max_steps=len(saved_actions) + 2)
            
            print(f"⚠️ Rerun completed unexpectedly")
            print(f"   Steps executed: {len(result.history) if hasattr(result, 'history') else 'Unknown'}")
            print(f"   Task done: {result.is_done() if hasattr(result, 'is_done') else 'Unknown'}")
            print(f"   Task successful: {result.is_successful() if hasattr(result, 'is_successful') else 'Unknown'}")
            
            # Check if it actually failed
            if hasattr(result, 'history') and len(result.history) < len(saved_actions):
                print(f"✅ EXPECTED FAILURE: Only {len(result.history)} steps executed out of {len(saved_actions)}")
                success = True
            else:
                print(f"❌ UNEXPECTED: All steps executed despite incorrect indices")
                success = False
                
        except Exception as execution_error:
            print(f"✅ EXPECTED FAILURE: Rerun failed with error: {execution_error}")
            success = True
        
        # 5. Cleanup
        await browser.close()
        
        return success
        
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("🧪 FAIL TEST CASE: Incorrect Indices in Rerun")
    print("=" * 60)
    
    # Test 1: Analyze the fail test data
    success1 = analyze_fail_test_data()
    
    # Test 2: Simulate the failure
    success2 = simulate_rerun_failure()
    
    # Test 3: Test actual rerun execution
    success3 = await test_actual_rerun_failure()
    
    print(f"\n🎯 RESULTS:")
    print(f"   Data analysis: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"   Failure simulation: {'✅ PASS (Failed as expected)' if not success2 else '❌ FAIL (Should have failed)'}")
    print(f"   Actual rerun test: {'✅ PASS' if success3 else '❌ FAIL'}")
    
    if success1 and not success2 and success3:
        print("\n🎉 FAIL TEST SUCCESSFUL!")
        print("✅ Rerun correctly fails when indices are incorrect")
        print("✅ Regression testing works as expected")
        print("✅ Index-based validation is working")
        return 0
    else:
        print("\n⚠️ Test results unexpected - review implementation")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
