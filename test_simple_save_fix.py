#!/usr/bin/env python3
"""
Test the simplified save function that just saves JSON data
"""

import sys
import os
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple_save_logic():
    print("🧪 Testing simplified save logic...")
    
    # Simulate the new simplified save logic
    test_steps = [
        {
            "step": 1,
            "action_type": "open_tab",
            "action_params": {"url": "https://www.youtube.com"},
            "evaluation": "Unknown - The previous task wasn't evaluated in the context of Xbox Cloud Gaming.",
            "memory": "Need to open YouTube and play the song '2002' by <PERSON><PERSON><PERSON>.",
            "next_goal": "Navigate to YouTube to perform the search."
        },
        {
            "step": 2,
            "action_type": "input_text",
            "action_params": {"index": 3, "text": "2002 song by <PERSON><PERSON><PERSON>"},
            "evaluation": "Success - Navigated to the YouTube website.",
            "memory": "Opened YouTube and prepared to search for the song '2002' by <PERSON><PERSON><PERSON>.",
            "next_goal": "Search for the song '2002' by <PERSON><PERSON><PERSON> on YouTube."
        },
        {
            "step": 3,
            "action_type": "click_element",
            "action_params": {"index": 8},
            "evaluation": "Success - '2002 song by <PERSON>-<PERSON>' entered in the search box.",
            "memory": "Song '2002' by <PERSON>-<PERSON> has been searched on YouTube.",
            "next_goal": "Click on the search result for '2002 song by anne-marie'."
        },
        {
            "step": 4,
            "action_type": "click_element",
            "action_params": {"index": 30},
            "evaluation": "Success - The first search result for '2002 song by Anne-<PERSON>' was clicked.",
            "memory": "Currently viewing search results for '2002 song by Anne-Marie' on YouTube. Preparing to play the official video.",
            "next_goal": "Play the official music video of '2002' by Anne-Marie."
        },
        {
            "step": 5,
            "action_type": "done",
            "action_params": {"text": "The YouTube video '2002' by Anne-Marie is successfully playing.", "success": True},
            "evaluation": "Success - The video 'Anne-Marie - 2002 [Official Video]' is playing.",
            "memory": "The song '2002' by Anne-Marie is currently playing.",
            "next_goal": "Verify that the video is playing and complete the task."
        }
    ]
    
    saved_actions = []
    
    for test_step in test_steps:
        # Apply the NEW SIMPLIFIED save logic (no DOM processing)
        action_data = {
            "step_number": test_step["step"],
            "action_type": test_step["action_type"],
            "action_params": test_step["action_params"],
            "llm_context": {
                "evaluation_previous_goal": test_step["evaluation"],
                "memory": test_step["memory"],
                "next_goal": test_step["next_goal"]
            }
        }
        
        saved_actions.append(action_data)
        
        # Show what would be saved
        index = test_step["action_params"].get("index")
        if index is not None:
            print(f"   ✅ Step {test_step['step']} ({test_step['action_type']}): Index {index} - SAVED SUCCESSFULLY")
        else:
            print(f"   ✅ Step {test_step['step']} ({test_step['action_type']}): No index - SAVED SUCCESSFULLY")
    
    print(f"\n📊 Results:")
    print(f"   Total steps processed: {len(test_steps)}")
    print(f"   Total actions saved: {len(saved_actions)}")
    print(f"   Success rate: {len(saved_actions)}/{len(test_steps)} = 100%")
    
    # Verify all steps were saved
    return len(saved_actions) == 5

def test_rerun_data_format():
    print("\n🧪 Testing rerun data format...")
    
    # Simulate saved action data in the new format
    saved_action = {
        "step_number": 2,
        "action_type": "input_text",
        "action_params": {"index": 3, "text": "2002 song by Anne-Marie"},
        "llm_context": {
            "evaluation_previous_goal": "Success - Navigated to the YouTube website.",
            "memory": "Opened YouTube and prepared to search for the song '2002' by Anne-Marie.",
            "next_goal": "Search for the song '2002' by Anne-Marie on YouTube."
        }
    }
    
    print(f"📋 Saved action format:")
    print(f"   Step: {saved_action['step_number']}")
    print(f"   Action: {saved_action['action_type']}")
    print(f"   Params: {saved_action['action_params']}")
    print(f"   Index: {saved_action['action_params'].get('index', 'None')}")
    
    # Test MockLLM usage
    print(f"\n🤖 MockLLM would return:")
    mock_response = {
        "current_state": saved_action["llm_context"],
        "action": [{
            saved_action["action_type"]: saved_action["action_params"]
        }]
    }
    print(f"   Response: {mock_response}")
    
    # Test agent execution
    action_to_execute = mock_response["action"][0]
    action_type = list(action_to_execute.keys())[0]
    action_params = action_to_execute[action_type]
    
    print(f"\n🎬 Agent would execute:")
    print(f"   Action: {action_type}")
    print(f"   Params: {action_params}")
    if "index" in action_params:
        print(f"   → Will use index {action_params['index']}")
        print(f"   → If index {action_params['index']} fails → FAIL TEST")
    
    return True

if __name__ == "__main__":
    print("🧪 Testing Simplified Save Function")
    print("=" * 60)
    
    success1 = test_simple_save_logic()
    success2 = test_rerun_data_format()
    
    if success1 and success2:
        print("\n🎉 All tests passed! Simplified save function should work perfectly.")
        print("\n🎯 Expected improvements:")
        print("   - All 5 steps will be saved (no DOM processing errors)")
        print("   - Simple JSON format with just essential data")
        print("   - Rerun will execute ALL 5 steps for proper regression testing")
        print("   - Index-based execution with fail-fast on index errors")
        exit(0)
    else:
        print("\n❌ Some tests failed.")
        exit(1)
