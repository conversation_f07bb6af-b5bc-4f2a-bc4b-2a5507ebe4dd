"""
Test the "Done" step logic for failed rerun reports to ensure no overlapping entries.

This tests two scenarios:
1. Task fails at step 0 (no steps executed) - should show single "Done" step
2. Task fails after some steps - should show executed steps + final "Done" step
"""

def test_done_step_logic():
    """Test the logic for adding Done steps in failed rerun reports"""
    
    print("🧪 Testing Done Step Logic for Failed Reruns\n")
    
    # Scenario 1: No steps executed (immediate failure)
    print("📋 Scenario 1: Task fails at step 0 (no steps executed)")
    test_report_data_empty = []
    failure_reason = "Browser connection failed"
    
    # Apply the fixed logic
    if not test_report_data_empty:
        # No steps were executed - show single "Done" step indicating immediate failure
        test_report_data_empty.append({
            "Action": "Done",
            "Goal": "Rerun completed",
            "evaluation": "Failed",
            "result": f"Task failed: {failure_reason}"
        })
    else:
        # Some steps were executed - add final "Done" step to show completion status
        test_report_data_empty.append({
            "Action": "Done",
            "Goal": "Rerun completed",
            "evaluation": "Failed",
            "result": "Task failed"
        })
    
    print(f"  Steps in report: {len(test_report_data_empty)}")
    for i, step in enumerate(test_report_data_empty, 1):
        print(f"    Step {i}: {step['Action']} - {step['Goal']} - {step['evaluation']} - {step['result']}")
    
    # Scenario 2: Some steps executed before failure
    print(f"\n📋 Scenario 2: Task fails after executing some steps")
    test_report_data_partial = [
        {
            "Action": "OpenTab",
            "Goal": "Search for 'Demon Slayer new movie' on Google",
            "evaluation": "Success",
            "result": "🔗 Opened new tab with http://www.google.com"
        },
        {
            "Action": "ClickElement",
            "Goal": "Click on search button",
            "evaluation": "Failed",
            "result": "Element not found"
        }
    ]
    
    # Apply the fixed logic
    if not test_report_data_partial:
        # No steps were executed - show single "Done" step indicating immediate failure
        test_report_data_partial.append({
            "Action": "Done",
            "Goal": "Rerun completed",
            "evaluation": "Failed",
            "result": f"Task failed: {failure_reason}"
        })
    else:
        # Some steps were executed - add final "Done" step to show completion status
        test_report_data_partial.append({
            "Action": "Done",
            "Goal": "Rerun completed",
            "evaluation": "Failed",
            "result": "Task failed"
        })
    
    print(f"  Steps in report: {len(test_report_data_partial)}")
    for i, step in enumerate(test_report_data_partial, 1):
        print(f"    Step {i}: {step['Action']} - {step['Goal']} - {step['evaluation']} - {step['result']}")
    
    # Validation
    print(f"\n✅ Validation Results:")
    
    # Check Scenario 1: Should have exactly 1 step
    if len(test_report_data_empty) == 1:
        print(f"  ✅ Scenario 1: Correct - Single 'Done' step for immediate failure")
    else:
        print(f"  ❌ Scenario 1: Error - Expected 1 step, got {len(test_report_data_empty)}")
    
    # Check Scenario 2: Should have 3 steps (2 executed + 1 Done)
    if len(test_report_data_partial) == 3:
        print(f"  ✅ Scenario 2: Correct - {len(test_report_data_partial)} steps (2 executed + 1 Done)")
    else:
        print(f"  ❌ Scenario 2: Error - Expected 3 steps, got {len(test_report_data_partial)}")
    
    # Check that both scenarios end with "Done" action
    scenario1_last = test_report_data_empty[-1]['Action'] == 'Done'
    scenario2_last = test_report_data_partial[-1]['Action'] == 'Done'
    
    if scenario1_last and scenario2_last:
        print(f"  ✅ Both scenarios end with 'Done' action")
    else:
        print(f"  ❌ Not all scenarios end with 'Done' action")
    
    return len(test_report_data_empty) == 1 and len(test_report_data_partial) == 3

def test_report_structure():
    """Test the final report structure"""
    
    print(f"\n📊 Testing Report Structure:")
    
    # Example of what the HTML report would show
    scenarios = [
        {
            "name": "Immediate Failure (Step 0)",
            "steps": [
                {"step": 1, "action": "Done", "goal": "Rerun completed", "evaluation": "Failed", "result": "Task failed: Browser connection failed"}
            ]
        },
        {
            "name": "Partial Execution Failure",
            "steps": [
                {"step": 1, "action": "OpenTab", "goal": "Search for 'Demon Slayer new movie' on Google", "evaluation": "Success", "result": "🔗 Opened new tab with http://www.google.com"},
                {"step": 2, "action": "ClickElement", "goal": "Click on search button", "evaluation": "Failed", "result": "Element not found"},
                {"step": 3, "action": "Done", "goal": "Rerun completed", "evaluation": "Failed", "result": "Task failed"}
            ]
        }
    ]
    
    for scenario in scenarios:
        print(f"\n  📋 {scenario['name']}:")
        print(f"     {'Step':<4} {'Action':<12} {'Goal':<30} {'Evaluation':<10} {'Result'}")
        print(f"     {'-'*4} {'-'*12} {'-'*30} {'-'*10} {'-'*20}")
        
        for step in scenario['steps']:
            print(f"     {step['step']:<4} {step['action']:<12} {step['goal'][:30]:<30} {step['evaluation']:<10} {step['result'][:20]}")

if __name__ == "__main__":
    print("🚀 Testing Done Step Logic for Failed Reruns\n")
    
    success = test_done_step_logic()
    test_report_structure()
    
    if success:
        print(f"\n🎉 All tests passed! The Done step logic prevents overlapping entries.")
        print(f"✅ No duplicate or redundant steps will appear in failed rerun reports.")
    else:
        print(f"\n💥 Tests failed! The logic needs adjustment.")
