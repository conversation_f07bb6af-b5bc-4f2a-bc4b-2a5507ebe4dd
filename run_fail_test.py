#!/usr/bin/env python3
"""
Simple script to run the fail test case through the rerun system
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def run_fail_test():
    print("🚨 RUNNING FAIL TEST CASE")
    print("=" * 50)
    print("📋 Test: Rerun with incorrect indices")
    print("📁 File: outputs/execution_data_for_rerun_FAIL_TEST.json")
    print("🎯 Expected: Rerun should fail at Step 2 (index 999)")
    print()
    
    try:
        # Import the rerun function
        from app import AuExecuteRerun
        
        # Execute the fail test
        print("🎬 Starting rerun execution...")
        result_df, gif_path = AuExecuteRerun("outputs/execution_data_for_rerun_FAIL_TEST.json")
        
        print("📊 Rerun Results:")
        print(result_df.to_string(index=False))
        
        if gif_path:
            print(f"🎬 GIF created: {gif_path}")
        else:
            print("⚠️ No GIF created")
            
        # Analyze results
        if len(result_df) < 8:  # Should fail before completing all 8 steps
            print("\n✅ FAIL TEST SUCCESSFUL!")
            print(f"   Only {len(result_df)} steps executed (expected < 8)")
            print("   Rerun correctly failed when incorrect index was encountered")
        else:
            print("\n❌ FAIL TEST FAILED!")
            print(f"   All {len(result_df)} steps executed unexpectedly")
            print("   Rerun should have failed at incorrect indices")
            
    except Exception as e:
        print(f"✅ EXPECTED FAILURE: {e}")
        print("   This is the expected behavior when indices are incorrect")

def show_test_summary():
    print("\n📋 FAIL TEST CASE SUMMARY")
    print("=" * 50)
    print("🎯 Purpose: Demonstrate index-based validation in rerun system")
    print()
    print("📊 Test Data Changes:")
    print("   Original → Fail Test")
    print("   Index 6  → Index 999  (input_text)")
    print("   Index 21 → Index 888  (click_element)")
    print("   Index 30 → Index 777  (click_element)")
    print("   Index 31 → Index 666  (click_element)")
    print("   Index 31 → Index 555  (click_element)")
    print("   Index 31 → Index 444  (click_element)")
    print()
    print("🔍 Expected Behavior:")
    print("   Step 1: ✅ open_tab succeeds (no index)")
    print("   Step 2: ❌ input_text fails (index 999 not found)")
    print("   Step 3-8: ⚠️ Never executed due to Step 2 failure")
    print()
    print("✅ Success Criteria:")
    print("   - Rerun stops at Step 2")
    print("   - Error indicates index 999 not found")
    print("   - Remaining steps are not executed")
    print("   - Demonstrates proper regression testing")

if __name__ == "__main__":
    show_test_summary()
    print("\n" + "="*50)
    asyncio.run(run_fail_test())
