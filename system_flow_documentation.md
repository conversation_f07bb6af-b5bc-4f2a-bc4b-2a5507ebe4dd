# 🏗️ VizCheck v2 - System Flow Documentation

## 📋 Table of Contents
1. [Initial Execution Flow](#initial-execution-flow)
2. [Rerun Execution Flow](#rerun-execution-flow)
3. [Data Processing Flows](#data-processing-flows)
4. [Error Handling Flows](#error-handling-flows)
5. [Report Generation Flow](#report-generation-flow)
6. [Performance Comparison](#performance-comparison)

---

## 🚀 Initial Execution Flow

### Main Execution Pipeline
```mermaid
graph TD
    A[User Input Task] --> B[run_agent Function]
    B --> C[Azure OpenAI LLM]
    C --> D[Agent Initialization]
    D --> E[Browser Launch]
    E --> F[Task Execution]
    F --> G[Action Processing]
    G --> H[Result Evaluation]
    H --> I{Task Complete?}
    I -->|No| F
    I -->|Yes| J[Save Successful Actions]
    J --> K[Generate Report]
    K --> L[Return DataFrame to Gradio]
```

### LLM Decision Making Process
```mermaid
graph TD
    A[Current Browser State] --> B[Take Screenshot]
    B --> C[Extract DOM Elements]
    C --> D[Send to Azure OpenAI]
    D --> E[LLM Analyzes Context]
    E --> F[Generate Next Action]
    F --> G[Execute Action]
    G --> H[Evaluate Result]
    H --> I{Goal Achieved?}
    I -->|No| A
    I -->|Yes| J[Move to Next Goal]
```

### Action Execution Cycle
```mermaid
graph LR
    A[LLM Planning] --> B[Action Generation]
    B --> C[Browser Execution]
    C --> D[Result Capture]
    D --> E[Success Evaluation]
    E --> F[History Storage]
    F --> A
```

---

## 🔄 Rerun Execution Flow

### Complete Rerun Pipeline
```mermaid
graph TD
    A[User Selects Rerun] --> B[run_rerun_agent Function]
    B --> C[Load Saved Actions from JSON]
    C --> D{Saved Actions Found?}
    D -->|No| E[Throw Exception]
    D -->|Yes| F[Create MockLLM]
    F --> G[Configure MockLLM with Saved Actions]
    G --> H[Create Browser Config]
    H --> I[Initialize Agent with MockLLM]
    I --> J[Pass Agent Reference to MockLLM]
    J --> K[Start Rerun Execution]
    K --> L[MockLLM Provides Pre-saved Actions]
    L --> M[Browser Executes Actions]
    M --> N[Check Action Result]
    N --> O{Action Successful?}
    O -->|Yes| P[Continue to Next Action]
    O -->|No| Q[Create RerunFailedResult]
    P --> R{More Actions?}
    R -->|Yes| L
    R -->|No| S[All Actions Completed]
    S --> T[Check Completion Status]
    T --> U{All Steps Executed?}
    U -->|Yes| V[Return Successful Result]
    U -->|No| W[Create RerunFailedResult - Incomplete]
    Q --> X[Return Failed Result]
    W --> X
    V --> Y[parse_result Function]
    X --> Y
    Y --> Z{Result Type?}
    Z -->|Failed| AA[Process Failed Rerun History]
    Z -->|Success| BB[Process Successful History]
    AA --> CC[Extract Partial Steps]
    BB --> DD[Extract All Steps + XPaths]
    CC --> EE[Add Final 'Done' Step - Failed]
    DD --> FF[Call parse_and_display_result]
    EE --> GG[Create Failed Report Data]
    FF --> HH[Create Success Report Data]
    GG --> II[update_html_report]
    HH --> II
    II --> JJ[Generate HTML Report]
    JJ --> KK[Generate PDF Report]
    KK --> LL[Return DataFrame to Gradio]
    E --> MM[Display Error]
```

### MockLLM vs Real LLM Comparison
```mermaid
graph LR
    A[Initial: Real Azure OpenAI] --> B[Generates New Actions]
    C[Rerun: MockLLM] --> D[Replays Saved Actions]
    
    B --> E[Variable Results]
    D --> F[Deterministic Results]
```

### MockLLM Action Replay Logic
```mermaid
graph TD
    A[MockLLM Called] --> B[Get Current Action Index]
    B --> C[Return Saved Action]
    C --> D[Increment Index]
    D --> E{More Actions?}
    E -->|Yes| F[Wait for Next Call]
    E -->|No| G[Signal Completion]
```

---

## 📊 Data Processing Flows

### Save Successful Actions Flow
```mermaid
graph TD
    A[Agent Execution Complete] --> B[Check Task Success]
    B --> C{Task Successful?}
    C -->|Yes| D[AuSaveSuccessfulActionsForRerun]
    C -->|No| E[Skip Saving]
    D --> F[Filter Execution History]
    F --> G[Check Each Step]
    G --> H{Step Has Errors?}
    H -->|No| I[Include in Saved Actions]
    H -->|Yes| J[Exclude from Saved Actions]
    I --> K[Extract Action Details]
    J --> L[Continue to Next Step]
    K --> L
    L --> M{More Steps?}
    M -->|Yes| G
    M -->|No| N[Create JSON Structure]
    N --> O[Save to File]
```

### Data Sources Comparison
```mermaid
graph LR
    A[Initial: User Task Input] --> B[LLM Planning]
    C[Rerun: JSON File] --> D[Pre-saved Actions]
    
    B --> E[Dynamic Decision Making]
    D --> F[Static Action Replay]
```

---

## ⚠️ Error Handling Flows

### Rerun Failure Detection Points
```mermaid
graph TD
    A[Action Execution] --> B{Browser Error?}
    B -->|Yes| C[RerunFailedResult - Action Error]
    B -->|No| D[Continue]
    D --> E{All Steps Done?}
    E -->|No| F[RerunFailedResult - Incomplete]
    E -->|Yes| G[Success]
```

### Error Type Classification
```mermaid
graph TD
    A[Rerun Execution] --> B{Error Type?}
    B -->|Action Failed| C[Step-Level Failure]
    B -->|Incomplete Execution| D[Execution-Level Failure]
    B -->|Exception| E[System-Level Failure]
    
    C --> F[RerunFailedResult with Partial History]
    D --> G[RerunFailedResult with Incomplete Steps]
    E --> H[RerunFailedResult with Empty History]
    
    F --> I[Show Steps + Done Failed]
    G --> I
    H --> J[Show Single Done Failed]
```

### Failed Report Logic Flow
```mermaid
graph TD
    A[Failed Rerun Result] --> B{Any Steps Executed?}
    B -->|No| C[Single 'Done' Step]
    B -->|Yes| D[Executed Steps + 'Done' Step]
    
    C --> E[Action: Done<br/>Goal: Rerun completed<br/>Evaluation: Failed<br/>Result: Task failed: specific error]
    
    D --> F[Step 1: Executed Action<br/>Step 2: Failed Action<br/>Step N: Done - Failed]
```

---

## 📄 Report Generation Flow

### Report Creation Pipeline
```mermaid
graph TD
    A[Execution Complete] --> B[parse_result Function]
    B --> C{Result Type?}
    C -->|Success| D[Extract Action History]
    C -->|Failed| E[Extract Partial History]
    
    D --> F[Get XPath Selectors]
    E --> G[Add Done Step]
    
    F --> H[parse_and_display_result]
    G --> I[Create test_report_data]
    
    H --> J[Process Evaluations]
    I --> K[Create Failed Report Structure]
    
    J --> L[Create Success Report Structure]
    K --> M[update_html_report]
    L --> M
    
    M --> N[Populate HTML Template]
    N --> O[Generate PDF]
    O --> P[Save Files]
    P --> Q[Return DataFrame]
```

### HTML Report Data Flow
```mermaid
graph LR
    A[test_report dict] --> B[update_html_report function]
    B --> C[HTML Template Processing]
    C --> D[PDF Generation]
    C --> E[HTML Report File]
    
    F[action_list] --> G[Detailed Steps Table]
    H[task] --> I[Report Header]
    J[status] --> K[Success/Fail Badge]
    L[execution_time] --> M[Performance Metrics]
```

---

## 📈 Performance Comparison

### Execution Speed Comparison
| Aspect | Initial Execution | Rerun Execution |
|--------|------------------|-----------------|
| **LLM Calls** | Multiple (per step) | Zero |
| **Planning Time** | ~2-5 seconds/step | ~0 seconds |
| **Action Source** | AI-generated | Pre-saved |
| **Variability** | High (AI decisions) | Zero (deterministic) |
| **Speed** | Slower | Faster |
| **Purpose** | Task completion | Validation/Testing |

### Resource Usage Flow
```mermaid
graph TD
    A[Initial Execution] --> B[High CPU - LLM Processing]
    A --> C[High Network - API Calls]
    A --> D[Variable Time - AI Thinking]
    
    E[Rerun Execution] --> F[Low CPU - No LLM]
    E --> G[Low Network - Local Data]
    E --> H[Fast Time - Direct Replay]
```

---

## 🎯 Key System Benefits

### Rerun Advantages
1. **⚡ Faster Execution**: No LLM inference time
2. **🎯 Consistent Actions**: Exact same actions replayed
3. **🔍 Better Debugging**: Can isolate browser/element issues
4. **📊 Detailed Failure Reports**: Shows exactly where it failed
5. **🔄 Reproducible Results**: Same actions, same sequence

### Use Case Scenarios
```mermaid
graph TD
    A[VizCheck System] --> B[Initial Execution]
    A --> C[Rerun Execution]
    
    B --> D[New Task Automation]
    B --> E[AI-Powered Decision Making]
    B --> F[Complex Problem Solving]
    
    C --> G[Regression Testing]
    C --> H[Performance Validation]
    C --> I[Debugging Element Issues]
    C --> J[Automated QA Checks]
```

---

## 🔧 Technical Implementation Details

### Browser_Use Module Integration
```mermaid
graph TD
    A[app.py] --> B[browser_use.Agent]
    B --> C[browser_use.Browser]
    B --> D[browser_use.Controller]

    C --> E[Playwright Browser Management]
    D --> F[Action Registry]
    F --> G[ClickElement, InputText, etc.]

    H[MockLLM] --> I[Replaces Real LLM in Rerun]
    I --> J[Provides Saved Actions]
    J --> K[Agent Executes Actions]
```

### Data Structure Flow
```mermaid
graph LR
    A[AgentHistory] --> B[ActionResult]
    B --> C[test_report_data]
    C --> D[HTML Report]

    E[Saved Actions JSON] --> F[MockLLM]
    F --> G[Action Replay]
    G --> H[New AgentHistory]
```

### File System Integration
```mermaid
graph TD
    A[Initial Execution] --> B[outputs/execution_data_timestamp.json]
    C[Rerun Execution] --> D[outputs/updated_test_report.html]
    C --> E[outputs/test_case_report.pdf]

    F[assets/html/test_report.html] --> G[Template for Reports]
    G --> D
```

---

## 🚨 Critical Fix Points

### Save Logic Fix (Lines 249-257)
**Problem**: Step 5 missing from rerun due to LLM evaluation filtering
**Solution**: Only check execution errors, ignore LLM context

```python
# BEFORE (Problematic)
step_successful = step_no_errors and (llm_indicates_success or not llm_indicates_failure)

# AFTER (Fixed)
step_successful = all(not result.error for result in history_item.result)
```

### Duplicate Class Elimination
**Problem**: 4 duplicate `FailedRerunResult` classes
**Solution**: Single unified `RerunFailedResult` class

### Overlapping Report Entries Fix
**Problem**: Redundant steps in failed reports
**Solution**: Smart "Done" step logic based on execution status

---

## 🎯 System Architecture Summary

### Component Interaction
```mermaid
graph TD
    A[Gradio UI] --> B[app.py Main Functions]
    B --> C[run_agent - Initial]
    B --> D[run_rerun_agent - Rerun]

    C --> E[Real LLM + Browser]
    D --> F[MockLLM + Browser]

    E --> G[AuSaveSuccessfulActionsForRerun]
    F --> H[parse_result]

    G --> I[JSON File Storage]
    H --> J[Report Generation]
    I --> D
    J --> A
```

### Key Success Metrics
1. **Accuracy**: Correct step inclusion in rerun data
2. **Performance**: Fast rerun execution without LLM overhead
3. **Reliability**: Consistent error handling and reporting
4. **Usability**: Clear, detailed reports for both success and failure cases

This documentation provides a comprehensive overview of both execution flows, their technical implementation, key fixes applied, and system architecture.
