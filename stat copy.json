[{"name": "xCloud.Client.App.HttpReliability", "data": {"baseCV": "m1xk6sn9UXRM9HoEfqRe5l", "cV": "m1xk6sn9UXRM9HoEfqRe5l.340", "clientAppType": "browser", "httpEnvironment": "prod", "hostname": "www.xbox.com", "xuid": "2535419187117242", "isXGPUSubscriber": true, "commitHash": "061ecab79a", "version": "29.1.42", "appName": "WebApp", "streamType": "CloudConsole", "platform": "Web", "deviceId": "03C42C924E9E45D094475AD2856CA0D9", "browserName": "edge", "fullBrowserVersion": "135.0.3179.54", "osName": "windows", "osVersion": "15.0.0", "mobileModel": "unknown", "mobileVendor": "unknown", "desktopSiteRequested": false, "deviceType": "desktop", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "isServer": false, "environmentName": "prod", "infraGroup": "xplat", "podName": "edgewater-b68c96877-mr8rd", "region": "northcentralus", "reliabilityReport": {"count5xx": 0, "countResponse": 1, "countError": 0, "sumLatencyMs": 1999, "hostData": [{"hostName": "chat.xboxlive.com", "count5xx": 0, "countResponse": 1, "countError": 0, "sumLatencyMs": 1999, "statusData": [{"count": 1, "sumLatencyMs": 1999, "maxLatencyMs": 1999, "minLatencyMs": 1999, "zeroLatencyCount": 0, "httpStatus": 200}], "errorData": []}]}}, "ext": {"app": {"id": "JS:xbox.com", "locale": "en", "sesId": "DmiTVzK9vw5B2ifdGnGWPu"}, "sdk": {"seq": 2, "installId": "1d5c2bf5-00be-40a2-a2f5-051d337ac184", "epoch": "3438295410"}, "user": {"locale": "en-US", "localId": "t:03C42C924E9E45D094475AD2856CA0D9"}, "web": {"domain": "www.xbox.com", "consentDetails": "{\"Required\":true,\"Analytics\":true,\"SocialMedia\":true,\"Advertising\":true,\"GPC_DataSharingOptIn\":true}", "userConsent": true}, "os": {}, "intweb": {"msfpc": "GUID=6ce7f940ec2a4fb78b931aad9a3d9d1a&HASH=6ce7&LV=202410&V=4&LU=1729661449038", "anid": ""}, "utc": {"popSample": 100}, "loc": {"tz": "+05:30"}, "device": {}}, "iKey": "o:d0e0bd479af0445eb368d83796596621", "time": "2025-04-09T10:20:33.488Z", "ver": "4.0"}, {"name": "xCloud.Client.App.Warn", "data": {"baseCV": "m1xk6sn9UXRM9HoEfqRe5l", "cV": "m1xk6sn9UXRM9HoEfqRe5l.340", "clientAppType": "browser", "httpEnvironment": "prod", "hostname": "www.xbox.com", "xuid": "2535419187117242", "isXGPUSubscriber": true, "commitHash": "061ecab79a", "version": "29.1.42", "appName": "WebApp", "streamType": "CloudConsole", "platform": "Web", "deviceId": "03C42C924E9E45D094475AD2856CA0D9", "browserName": "edge", "fullBrowserVersion": "135.0.3179.54", "osName": "windows", "osVersion": "15.0.0", "mobileModel": "unknown", "mobileVendor": "unknown", "desktopSiteRequested": false, "deviceType": "desktop", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "isServer": false, "environmentName": "prod", "infraGroup": "xplat", "podName": "edgewater-b68c96877-mr8rd", "region": "northcentralus", "context": "ChatSocketSagaManager", "message": "Encountered message that no plugin could handle on channel: {\"type\":\"Party\",\"id\":\"2535419187117242\"}, type: UserInfo", "error": {"name": "Error", "message": "Encountered message that no plugin could handle on channel: {\"type\":\"Party\",\"id\":\"2535419187117242\"}, type: UserInfo", "stack": "Error: Encountered message that no plugin could handle on channel: {\"type\":\"Party\",\"id\":\"2535419187117242\"}, type: UserInfo\n    at je.log (https://assets.play.xbox.com/playxbox/static/js/xcloud-partner.2d31a5d6.chunk.js:1:56928)\n    at https://assets.play.xbox.com/playxbox/static/js/3739.c9b980cf.chunk.js:1:11356\n    at Array.forEach (<anonymous>)\n    at i.log (https://assets.play.xbox.com/playxbox/static/js/3739.c9b980cf.chunk.js:1:11342)\n    at v.handleSocketMessage (https://assets.play.xbox.com/playxbox/static/js/2092.45f6b295.chunk.js:1:3816)\n    at handleSocketMessage.next (<anonymous>)\n    at h (https://assets.play.xbox.com/playxbox/static/js/267.fd96a912.js:2:337338)\n    at Y (https://assets.play.xbox.com/playxbox/static/js/267.fd96a912.js:2:337138)\n    at https://assets.play.xbox.com/playxbox/static/js/267.fd96a912.js:2:333760\n    at h (https://assets.play.xbox.com/playxbox/static/js/267.fd96a912.js:2:330077)"}, "extra": []}, "ext": {"app": {"id": "JS:xbox.com", "locale": "en", "sesId": "DmiTVzK9vw5B2ifdGnGWPu"}, "sdk": {"seq": 2, "installId": "1d5c2bf5-00be-40a2-a2f5-051d337ac184", "epoch": "3438295410"}, "user": {"locale": "en-US", "localId": "t:03C42C924E9E45D094475AD2856CA0D9"}, "web": {"domain": "www.xbox.com", "consentDetails": "{\"Required\":true,\"Analytics\":true,\"SocialMedia\":true,\"Advertising\":true,\"GPC_DataSharingOptIn\":true}", "userConsent": true}, "os": {}, "intweb": {"msfpc": "GUID=6ce7f940ec2a4fb78b931aad9a3d9d1a&HASH=6ce7&LV=202410&V=4&LU=1729661449038", "anid": ""}, "utc": {"popSample": 100}, "loc": {"tz": "+05:30"}, "device": {}}, "iKey": "o:d0e0bd479af0445eb368d83796596621", "time": "2025-04-09T10:20:33.769Z", "ver": "4.0"}]