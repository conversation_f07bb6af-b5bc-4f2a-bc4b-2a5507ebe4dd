#!/usr/bin/env python3
"""
Test the save function fix for DOMHistoryElement attribute access
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_dom_element_access():
    print("🧪 Testing DOMHistoryElement attribute access...")
    
    try:
        # Import the DOMHistoryElement class
        from browser_use.dom.history_tree_processor.view import DOMHistoryElement
        
        # Create a mock DOMHistoryElement (it's a dataclass)
        mock_element = DOMHistoryElement(
            tag_name="button",
            xpath="//button[@id='test']",
            highlight_index=5,
            entire_parent_branch_path=["html", "body", "div"],
            attributes={"id": "test", "class": "btn"},
            shadow_root=False
        )
        
        print("✅ Created mock DOMHistoryElement")
        
        # Test the OLD way (should fail)
        try:
            xpath_old = mock_element.get("xpath")  # This should fail
            print("❌ OLD method worked - this shouldn't happen!")
            return False
        except AttributeError as e:
            print(f"✅ OLD method failed as expected: {e}")
        
        # Test the NEW way (should work)
        try:
            xpath_new = getattr(mock_element, "xpath", None)
            tag_name_new = getattr(mock_element, "tag_name", None)
            attributes_new = getattr(mock_element, "attributes", {})
            
            print(f"✅ NEW method works:")
            print(f"   xpath: {xpath_new}")
            print(f"   tag_name: {tag_name_new}")
            print(f"   attributes: {attributes_new}")
            
            return True
            
        except Exception as e:
            print(f"❌ NEW method failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        return False

def test_save_function_logic():
    print("\n🧪 Testing save function logic simulation...")
    
    try:
        # Simulate the fixed save function logic
        from browser_use.dom.history_tree_processor.view import DOMHistoryElement
        
        # Mock element like what would be in history_item.state.interacted_element
        mock_element = DOMHistoryElement(
            tag_name="input",
            xpath="//input[@id='search']",
            highlight_index=3,
            entire_parent_branch_path=["html", "body", "form"],
            attributes={"id": "search", "type": "text"},
            shadow_root=False
        )
        
        # Simulate the fixed logic
        action_data = {}
        
        if mock_element:
            # NEW FIXED logic - use getattr instead of .get()
            action_data["xpath"] = getattr(mock_element, "xpath", None)
            action_data["element_info"] = {
                "xpath": getattr(mock_element, "xpath", None),
                "tag_name": getattr(mock_element, "tag_name", None),
                "attributes": getattr(mock_element, "attributes", {}),
                "original_index": 3,  # Mock index
                "text_content": getattr(mock_element, "text_content", ""),
                "is_visible": True
            }
            
        print("✅ Save function logic simulation successful:")
        print(f"   action_data: {action_data}")
        
        # Verify all expected fields are present
        expected_fields = ["xpath", "element_info"]
        for field in expected_fields:
            if field in action_data:
                print(f"   ✅ {field}: Present")
            else:
                print(f"   ❌ {field}: Missing")
                return False
                
        return True
        
    except Exception as e:
        print(f"❌ Save function logic test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Save Function Fix for DOMHistoryElement")
    print("=" * 60)
    
    success1 = test_dom_element_access()
    success2 = test_save_function_logic()
    
    if success1 and success2:
        print("\n🎉 All tests passed! The save function fix should work.")
        print("\n🎯 Expected improvements:")
        print("   - No more 'DOMHistoryElement' object has no attribute 'get' errors")
        print("   - All 5 steps should be saved properly for regression testing")
        print("   - Rerun should execute ALL original steps, not just first and last")
        exit(0)
    else:
        print("\n❌ Some tests failed. Fix needs adjustment.")
        exit(1)
