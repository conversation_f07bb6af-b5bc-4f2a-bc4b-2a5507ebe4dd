#!/usr/bin/env python3
"""
Complete system check for both save function improvements and overall rerun system
"""

import sys
import os
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_1_save_function_improvements():
    print("🔍 CHECK 1: Save Function Improvements")
    print("=" * 50)
    
    try:
        # Analyze current saved data with improved logic
        with open('outputs/execution_data_for_rerun.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        actions = data.get('successful_actions', [])
        print(f"📊 Current saved data: {len(actions)} actions")
        
        # Apply improved filtering logic to existing data
        filtered_actions = []
        
        for i, action in enumerate(actions):
            llm_context = action.get('llm_context', {})
            evaluation = llm_context.get('evaluation_previous_goal', '').lower()
            
            # Apply improved logic
            llm_indicates_success = any(indicator in evaluation for indicator in ['success', 'successful', 'completed', 'achieved'])
            llm_indicates_failure = any(indicator in evaluation for indicator in ['failed', 'failure', 'error', 'unsuccessful', 'wrong'])
            
            # Assume no execution errors since they're in "successful_actions"
            step_no_errors = True
            step_successful = step_no_errors and (llm_indicates_success or not llm_indicates_failure)
            
            if step_successful:
                filtered_actions.append(action)
                print(f"   ✅ Action {i+1} ({action['action_type']}): KEEP - {evaluation}")
            else:
                print(f"   ❌ Action {i+1} ({action['action_type']}): FILTER OUT - {evaluation}")
                
        print(f"\n📈 Filtering results:")
        print(f"   Original actions: {len(actions)}")
        print(f"   After filtering: {len(filtered_actions)}")
        print(f"   Improvement: {len(actions) - len(filtered_actions)} failed actions removed")
        
        return len(filtered_actions) < len(actions)  # True if filtering removed some actions
        
    except Exception as e:
        print(f"❌ Error in save function check: {e}")
        return False

def check_2_rerun_system_health():
    print("\n🔍 CHECK 2: Rerun System Health")
    print("=" * 50)
    
    try:
        # Check if all required components are working
        checks = []
        
        # Check 1: MockLLM functionality
        try:
            from app import MockLLM
            mock_llm = MockLLM()
            if hasattr(mock_llm, 'with_structured_output'):
                checks.append(("MockLLM with_structured_output", True, "✅ Available"))
            else:
                checks.append(("MockLLM with_structured_output", False, "❌ Missing"))
        except Exception as e:
            checks.append(("MockLLM import", False, f"❌ Error: {e}"))
            
        # Check 2: Data loading functionality
        try:
            from browser_use.agent.service import AuLoadExecutionDataForRerun
            actions = AuLoadExecutionDataForRerun('outputs/execution_data_for_rerun.json')
            if actions and len(actions) > 0:
                checks.append(("Data loading", True, f"✅ Loaded {len(actions)} actions"))
            else:
                checks.append(("Data loading", False, "❌ No actions loaded"))
        except Exception as e:
            checks.append(("Data loading", False, f"❌ Error: {e}"))
            
        # Check 3: Save function availability
        try:
            from app import AuSaveSuccessfulActionsForRerun
            checks.append(("Save function", True, "✅ Available"))
        except Exception as e:
            checks.append(("Save function", False, f"❌ Error: {e}"))
            
        # Check 4: Agent creation functionality
        try:
            from app import AuCreateMockLLM, AuCreateRerunAgent
            checks.append(("Agent creation", True, "✅ Available"))
        except Exception as e:
            checks.append(("Agent creation", False, f"❌ Error: {e}"))
            
        # Print results
        all_passed = True
        for check_name, passed, message in checks:
            print(f"   {message} {check_name}")
            if not passed:
                all_passed = False
                
        return all_passed
        
    except Exception as e:
        print(f"❌ Error in system health check: {e}")
        return False

def check_3_integration_readiness():
    print("\n🔍 CHECK 3: Integration Readiness")
    print("=" * 50)
    
    try:
        # Check if the system is ready for end-to-end testing
        readiness_checks = []
        
        # Check if outputs directory exists
        if os.path.exists('outputs'):
            readiness_checks.append(("Outputs directory", True, "✅ Exists"))
        else:
            readiness_checks.append(("Outputs directory", False, "❌ Missing"))
            
        # Check if saved data exists
        if os.path.exists('outputs/execution_data_for_rerun.json'):
            readiness_checks.append(("Saved execution data", True, "✅ Available"))
        else:
            readiness_checks.append(("Saved execution data", False, "❌ Missing"))
            
        # Check if app.py is importable
        try:
            import app
            readiness_checks.append(("App module", True, "✅ Importable"))
        except Exception as e:
            readiness_checks.append(("App module", False, f"❌ Error: {e}"))
            
        # Print results
        all_ready = True
        for check_name, ready, message in readiness_checks:
            print(f"   {message} {check_name}")
            if not ready:
                all_ready = False
                
        return all_ready
        
    except Exception as e:
        print(f"❌ Error in integration readiness check: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Complete System Check")
    print("=" * 60)
    
    check1_passed = check_1_save_function_improvements()
    check2_passed = check_2_rerun_system_health()
    check3_passed = check_3_integration_readiness()
    
    print(f"\n🎯 SUMMARY:")
    print(f"   Save Function Improvements: {'✅ PASS' if check1_passed else '❌ FAIL'}")
    print(f"   Rerun System Health: {'✅ PASS' if check2_passed else '❌ FAIL'}")
    print(f"   Integration Readiness: {'✅ PASS' if check3_passed else '❌ FAIL'}")
    
    if check1_passed and check2_passed and check3_passed:
        print("\n🎉 ALL CHECKS PASSED! System is ready for improved rerun functionality.")
        exit(0)
    else:
        print("\n⚠️ Some checks failed. Review issues above.")
        exit(1)
