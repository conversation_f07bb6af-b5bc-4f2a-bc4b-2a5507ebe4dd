#!/usr/bin/env python3
"""
Compare working execution data vs fail test data to show index differences
"""

import json

def compare_execution_data():
    print("🔍 COMPARISON: Working vs Fail Test Data")
    print("=" * 80)
    
    # Load both files
    with open('outputs/execution_data_for_rerun.json', 'r', encoding='utf-8') as f:
        working_data = json.load(f)
    
    with open('outputs/execution_data_for_rerun_FAIL_TEST.json', 'r', encoding='utf-8') as f:
        fail_data = json.load(f)
    
    working_actions = working_data.get('successful_actions', [])
    fail_actions = fail_data.get('successful_actions', [])
    
    print(f"📊 METADATA COMPARISON:")
    print(f"   Working Task: {working_data['metadata']['task']}")
    print(f"   Fail Task:    {fail_data['metadata']['task']}")
    print(f"   Working Actions: {len(working_actions)}")
    print(f"   Fail Actions:    {len(fail_actions)}")
    
    print(f"\n📋 DETAILED ACTION COMPARISON:")
    print(f"{'Step':<6} {'Action Type':<15} {'Working Index':<15} {'Fail Index':<12} {'Status':<20}")
    print("=" * 80)
    
    for i in range(max(len(working_actions), len(fail_actions))):
        if i < len(working_actions):
            working = working_actions[i]
            working_step = working.get('step_number', i+1)
            working_type = working.get('action_type', 'N/A')
            working_index = working.get('action_params', {}).get('index', 'N/A')
        else:
            working_step = 'N/A'
            working_type = 'N/A'
            working_index = 'N/A'
            
        if i < len(fail_actions):
            fail = fail_actions[i]
            fail_step = fail.get('step_number', i+1)
            fail_type = fail.get('action_type', 'N/A')
            fail_index = fail.get('action_params', {}).get('index', 'N/A')
        else:
            fail_step = 'N/A'
            fail_type = 'N/A'
            fail_index = 'N/A'
        
        # Determine status
        if working_type != fail_type:
            status = "❌ TYPE MISMATCH"
        elif working_type in ['input_text', 'click_element']:
            if working_index == fail_index:
                status = "✅ SAME INDEX"
            else:
                status = "🚨 INDEX CHANGED"
        else:
            status = "✅ NO INDEX NEEDED"
            
        step_display = f"{working_step}" if working_step == fail_step else f"{working_step}→{fail_step}"
        print(f"{step_display:<6} {working_type:<15} {working_index:<15} {fail_index:<12} {status:<20}")
    
    print(f"\n🎯 EXPECTED RERUN BEHAVIOR:")
    print(f"   Step 1 (open_tab):     ✅ Will succeed - no index needed")
    print(f"   Step 2 (input_text):   ❌ Will fail - index 999 doesn't exist")
    print(f"   Step 3 (click_element): ❌ Will fail - index 888 doesn't exist")
    print(f"   Step 4 (click_element): ❌ Will fail - index 777 doesn't exist")
    print(f"   Step 5 (click_element): ❌ Will fail - index 666 doesn't exist")
    print(f"   Step 6 (click_element): ❌ Will fail - index 555 doesn't exist")
    print(f"   Step 7 (click_element): ❌ Will fail - index 444 doesn't exist")
    print(f"   Step 8 (done):          ⚠️ Never reached due to earlier failures")
    
    print(f"\n🔬 REGRESSION TEST VALIDATION:")
    print(f"   ✅ Working data uses real indices from successful execution")
    print(f"   ❌ Fail data uses impossible indices (999, 888, 777, etc.)")
    print(f"   🎯 Rerun should fail immediately at Step 2 when index 999 not found")
    print(f"   🚨 This demonstrates proper index-based validation")
    
    print(f"\n📝 KEY DIFFERENCES:")
    differences = []
    for i, (working, fail) in enumerate(zip(working_actions, fail_actions)):
        working_index = working.get('action_params', {}).get('index')
        fail_index = fail.get('action_params', {}).get('index')
        action_type = working.get('action_type')
        
        if working_index != fail_index and working_index is not None:
            differences.append({
                'step': working.get('step_number', i+1),
                'action': action_type,
                'working': working_index,
                'fail': fail_index
            })
    
    for diff in differences:
        print(f"   Step {diff['step']} ({diff['action']}): {diff['working']} → {diff['fail']}")
    
    print(f"\n🎉 CONCLUSION:")
    print(f"   This fail test case proves that the rerun system correctly")
    print(f"   validates indices and fails fast when elements are not found.")
    print(f"   Perfect for regression testing to catch UI changes!")

if __name__ == "__main__":
    compare_execution_data()
